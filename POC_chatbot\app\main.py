"""
FastAPI Chatbot Application Entry Point

This is the main application file that initializes the FastAPI app,
configures middleware, includes routers, and sets up the server.
"""

import logging
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from app.config import settings
from app.utils.logging import setup_logging
from app.middleware.error_handler import ErrorHandlerMiddleware
from app.middleware.logging import LoggingMiddleware


# Setup logging before importing other modules
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logger.info("Starting FastAPI Chatbot Application")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"SMS enabled: {settings.enable_sms}")
    logger.info(f"Voice enabled: {settings.enable_voice}")
    logger.info(f"Voice streaming enabled: {settings.enable_voice_streaming}")
    
    # Initialize services here if needed
    # await initialize_services()

    # Initialize audio files for basic voice responses
    try:
        from app.utils.audio import audio_manager
        await audio_manager.create_placeholder_audio_files()
        logger.info("Audio files initialized successfully")
    except Exception as e:
        logger.warning(f"Could not initialize audio files: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down FastAPI Chatbot Application")
    # Cleanup services here if needed
    # await cleanup_services()


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Create FastAPI app with lifespan manager
    app = FastAPI(
        title="FastAPI Chatbot",
        description="A comprehensive chatbot system with SMS and Voice channels",
        version="1.0.0",
        debug=settings.debug,
        lifespan=lifespan,
    )
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )
    
    # Add custom middleware
    if settings.enable_request_logging:
        app.add_middleware(LoggingMiddleware)
    
    app.add_middleware(ErrorHandlerMiddleware)
    
    # Mount static files for audio serving
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # Health check endpoints
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "environment": settings.environment,
            "version": "1.0.0"
        }
    
    @app.get("/ready")
    async def readiness_check():
        """Readiness check endpoint."""
        # Add checks for external services here
        checks = {
            "database": "ok",  # Placeholder
            "redis": "ok",     # Placeholder
            "twilio": "ok",    # Placeholder
        }
        
        all_healthy = all(status == "ok" for status in checks.values())
        status_code = 200 if all_healthy else 503
        
        return JSONResponse(
            content={
                "status": "ready" if all_healthy else "not_ready",
                "checks": checks
            },
            status_code=status_code
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "message": "FastAPI Chatbot API",
            "version": "1.0.0",
            "environment": settings.environment,
            "features": {
                "sms": settings.enable_sms,
                "voice": settings.enable_voice,
                "voice_streaming": settings.enable_voice_streaming,
            },
            "endpoints": {
                "health": "/health",
                "ready": "/ready",
                "sms_webhook": "/sms-webhook" if settings.enable_sms else None,
                "voice_webhook": "/voice-webhook" if settings.enable_voice else None,
                "media_stream": "/media-stream" if settings.enable_voice_streaming else None,
            }
        }
    
    # Include API routers
    include_routers(app)
    
    return app


def include_routers(app: FastAPI) -> None:
    """
    Include API routers based on enabled features.
    
    Args:
        app: FastAPI application instance
    """
    try:
        # Import routers only when needed to avoid circular imports
        if settings.enable_sms or settings.enable_voice:
            from app.api.webhooks import router as webhooks_router
            app.include_router(webhooks_router, tags=["webhooks"])
            logger.info("Webhooks router included")
        
        if settings.enable_voice_streaming:
            from app.api.websockets import router as websockets_router
            app.include_router(websockets_router, tags=["websockets"])
            logger.info("WebSockets router included")

        # Always include the web chat API for frontend interfaces
        from app.api.chat import router as chat_router
        app.include_router(chat_router, tags=["chat"])
        logger.info("Chat API router included")

    except ImportError as e:
        logger.warning(f"Could not import router: {e}")
        logger.warning("Some features may not be available")


# Create the FastAPI app instance
app = create_app()


# Development server entry point
if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"Starting server on {settings.host}:{settings.port}")
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=settings.enable_request_logging,
    )
