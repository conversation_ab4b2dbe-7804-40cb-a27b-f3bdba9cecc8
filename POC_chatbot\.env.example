# FastAPI Chatbot Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Server host and port
HOST=0.0.0.0
PORT=5050

# Environment (development, staging, production)
ENVIRONMENT=development

# Debug mode (true/false)
DEBUG=true

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# =============================================================================
# TWILIO CONFIGURATION
# =============================================================================
# Twilio Account SID (starts with AC...)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here

# Twilio Auth Token
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here

# Your Twilio phone number (format: +**********)
TWILIO_PHONE_NUMBER=+**********

# Webhook validation (true/false) - set to false for development
TWILIO_VALIDATE_WEBHOOKS=false

# =============================================================================
# ELEVENLABS CONFIGURATION
# =============================================================================
# ElevenLabs API Key
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# ElevenLabs Voice ID (default voice)
ELEVENLABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM

# ElevenLabs Model ID
ELEVENLABS_MODEL_ID=eleven_monolingual_v1

# Voice settings
ELEVENLABS_STABILITY=0.5
ELEVENLABS_SIMILARITY_BOOST=0.8
ELEVENLABS_STYLE=0.0
ELEVENLABS_USE_SPEAKER_BOOST=true

# =============================================================================
# GROQ CONFIGURATION
# =============================================================================
# Groq API Key for LLM (Language Model)
GROQ_API_KEY=your_groq_api_key_here

# Groq Model for Chat
GROQ_MODEL=llama3-8b-8192

# Groq Temperature setting (0.0 to 2.0)
GROQ_TEMPERATURE=0.7

# Groq Max Tokens
GROQ_MAX_TOKENS=1024

# =============================================================================
# ELEVENLABS STT CONFIGURATION
# =============================================================================
# ElevenLabs STT Model (uses same API key as TTS)
ELEVENLABS_STT_MODEL=eleven_multilingual_v2

# =============================================================================
# CHATBOT CONFIGURATION
# =============================================================================
# Default chatbot system message
CHATBOT_SYSTEM_MESSAGE=You are a helpful AI assistant. Keep responses concise and friendly.

# Maximum conversation history length
MAX_CONVERSATION_HISTORY=10

# Session timeout in minutes
SESSION_TIMEOUT_MINUTES=30

# =============================================================================
# AUDIO CONFIGURATION
# =============================================================================
# Audio format for Twilio (mulaw, linear16)
TWILIO_AUDIO_FORMAT=mulaw

# Sample rate for audio processing
AUDIO_SAMPLE_RATE=8000

# Audio chunk size for streaming
AUDIO_CHUNK_SIZE=1024

# =============================================================================
# REDIS CONFIGURATION (Optional - for session storage)
# =============================================================================
# Redis URL (uncomment if using Redis for session storage)
# REDIS_URL=redis://localhost:6379/0

# Redis password (if required)
# REDIS_PASSWORD=your_redis_password

# =============================================================================
# DATABASE CONFIGURATION (Optional - for persistent storage)
# =============================================================================
# Database URL (uncomment if using database)
# DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/chatbot_db

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Secret key for session encryption (generate a secure random string)
SECRET_KEY=your_secret_key_here_change_this_in_production

# CORS origins (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# Enable request logging (true/false)
ENABLE_REQUEST_LOGGING=true

# Enable performance monitoring (true/false)
ENABLE_MONITORING=true

# Sentry DSN for error tracking (optional)
# SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# EXTERNAL WEBHOOK URLS (for development with ngrok)
# =============================================================================
# Base URL for webhooks (set this to your ngrok URL during development)
# WEBHOOK_BASE_URL=https://your-ngrok-url.ngrok.app

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable SMS channel (true/false)
ENABLE_SMS=true

# Enable Voice channel (true/false)
ENABLE_VOICE=true

# Enable real-time voice streaming (true/false)
ENABLE_VOICE_STREAMING=true

# Enable session persistence (true/false)
ENABLE_SESSION_PERSISTENCE=false
