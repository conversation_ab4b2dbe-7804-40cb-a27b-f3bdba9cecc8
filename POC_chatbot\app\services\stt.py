"""
Speech-to-Text (STT) service for the FastAPI chatbot application.

This service provides speech recognition capabilities using ElevenLabs
STT API for high-quality speech recognition.
"""

import asyncio
import io
import time
from typing import Any, Dict, Optional, AsyncIterator
import httpx
from elevenlabs import AsyncElevenLabs

from app.services.base import BaseSTTService, ServiceHealth, ServiceStatus
from app.utils.audio import convert_audio_format, validate_audio_format
from app.config import settings, get_elevenlabs_stt_config
from app.utils.logging import get_logger

logger = get_logger(__name__)


class ElevenLabsSTTService(BaseSTTService):
    """
    ElevenLabs Speech-to-Text service implementation.

    Provides speech recognition using ElevenLabs STT API
    for converting audio to text in real-time conversations.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__("elevenlabs_stt", config)

        # Validate required configuration
        required_keys = ["api_key", "model"]
        self._validate_config(required_keys)

        # Initialize ElevenLabs client
        self.client: Optional[AsyncElevenLabs] = None
        self.api_key = config["api_key"]
        self.model = config.get("model", "eleven_multilingual_v2")

        # Audio processing settings
        self.supported_formats = ["wav", "mp3", "m4a", "ogg", "flac", "webm"]
        self.max_file_size = 25 * 1024 * 1024  # 25MB limit
        self.chunk_duration = 30  # seconds
    
    async def initialize(self) -> None:
        """Initialize the ElevenLabs STT service."""
        await super().initialize()

        try:
            # Initialize ElevenLabs async client
            self.client = AsyncElevenLabs(api_key=self.api_key)

            # Test the connection by checking user info
            user_info = await self.client.user.get()

            self.logger.info(
                "ElevenLabs STT service initialized successfully",
                model=self.model,
                supported_formats=self.supported_formats,
                user_tier=getattr(user_info, 'subscription', {}).get('tier', 'unknown')
            )

        except Exception as e:
            self.logger.error(f"Failed to initialize ElevenLabs STT service: {e}")
            raise
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the ElevenLabs STT service."""
        if not self.client:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message="ElevenLabs STT client not initialized"
            )

        try:
            start_time = time.time()

            # Check service health by getting user info
            user_info = await self.client.user.get()

            response_time = (time.time() - start_time) * 1000

            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="ElevenLabs STT service is operational",
                details={
                    "model": self.model,
                    "supported_formats": self.supported_formats,
                    "max_file_size_mb": self.max_file_size // (1024 * 1024),
                    "user_tier": getattr(user_info, 'subscription', {}).get('tier', 'unknown')
                },
                response_time_ms=response_time
            )

        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"ElevenLabs STT health check failed: {str(e)}"
            )
    
    async def transcribe_audio(
        self,
        audio_data: bytes,
        format: str = "wav",
        language: Optional[str] = None,
        **kwargs: Any
    ) -> str:
        """
        Transcribe audio data to text using ElevenLabs STT.

        Args:
            audio_data: Audio data as bytes
            format: Audio format (wav, mp3, etc.)
            language: Language code (optional, auto-detect if None)
            **kwargs: Additional parameters

        Returns:
            Transcribed text
        """
        if not self.client:
            raise RuntimeError("ElevenLabs STT client not initialized")

        # Validate format
        if not validate_audio_format(format):
            raise ValueError(f"Unsupported audio format: {format}")

        # Check file size
        if len(audio_data) > self.max_file_size:
            raise ValueError(f"Audio file too large: {len(audio_data)} bytes (max: {self.max_file_size})")

        self.logger.info(
            "Transcribing audio with ElevenLabs STT",
            format=format,
            size_bytes=len(audio_data),
            model=self.model
        )
        
        try:
            # Create file-like object from audio data
            audio_file = io.BytesIO(audio_data)
            audio_file.name = f"audio.{format}"

            # Call ElevenLabs STT API
            start_time = time.time()

            # Use the speech-to-text endpoint
            response = await self.client.speech_to_text.convert(
                audio=audio_file,
                model_id=self.model,
                **kwargs
            )

            processing_time = time.time() - start_time

            # Extract text from response
            if hasattr(response, 'text'):
                text = response.text.strip()
            elif hasattr(response, 'transcript'):
                text = response.transcript.strip()
            else:
                text = str(response).strip()

            self.logger.info(
                "Audio transcription completed",
                text_length=len(text),
                processing_time_ms=round(processing_time * 1000, 2),
                preview=text[:100] + "..." if len(text) > 100 else text
            )

            return text

        except Exception as e:
            self.logger.error(
                "Error transcribing audio with ElevenLabs",
                error=str(e),
                format=format,
                size_bytes=len(audio_data)
            )
            raise
    
    async def stream_transcription(
        self, 
        audio_stream: AsyncIterator[bytes],
        format: str = "wav",
        language: Optional[str] = None,
        **kwargs: Any
    ) -> AsyncIterator[str]:
        """
        Stream audio transcription in real-time.
        
        Note: OpenAI Whisper API doesn't support true streaming,
        so this implementation buffers audio chunks and transcribes them.
        
        Args:
            audio_stream: Stream of audio data chunks
            format: Audio format
            language: Language code (optional)
            **kwargs: Additional parameters
            
        Yields:
            Transcribed text chunks
        """
        self.logger.info("Starting streaming transcription")
        
        audio_buffer = bytearray()
        chunk_size_bytes = 8000 * self.chunk_duration  # Approximate chunk size
        
        try:
            async for audio_chunk in audio_stream:
                audio_buffer.extend(audio_chunk)
                
                # Process when buffer reaches chunk size
                if len(audio_buffer) >= chunk_size_bytes:
                    chunk_data = bytes(audio_buffer[:chunk_size_bytes])
                    audio_buffer = audio_buffer[chunk_size_bytes:]
                    
                    try:
                        # Transcribe chunk
                        text = await self.transcribe_audio(
                            chunk_data, 
                            format=format, 
                            language=language,
                            **kwargs
                        )
                        
                        if text:
                            yield text
                            
                    except Exception as e:
                        self.logger.error(f"Error transcribing audio chunk: {e}")
                        # Continue processing other chunks
                        continue
            
            # Process remaining buffer
            if len(audio_buffer) > 1000:  # Only process substantial remaining audio
                try:
                    text = await self.transcribe_audio(
                        bytes(audio_buffer), 
                        format=format, 
                        language=language,
                        **kwargs
                    )
                    
                    if text:
                        yield text
                        
                except Exception as e:
                    self.logger.error(f"Error transcribing final audio chunk: {e}")
        
        except Exception as e:
            self.logger.error(f"Error in streaming transcription: {e}")
            raise
    
    def preprocess_audio_for_whisper(
        self, 
        audio_data: bytes, 
        input_format: str,
        target_sample_rate: int = 16000
    ) -> bytes:
        """
        Preprocess audio data for optimal Whisper performance.
        
        Args:
            audio_data: Raw audio data
            input_format: Input audio format
            target_sample_rate: Target sample rate for Whisper
            
        Returns:
            Preprocessed audio data
        """
        try:
            # Convert to WAV format if needed (Whisper works well with WAV)
            if input_format.lower() != "wav":
                audio_data = convert_audio_format(
                    audio_data, 
                    input_format, 
                    "wav",
                    sample_rate=target_sample_rate
                )
            
            self.logger.debug(
                "Audio preprocessed for Whisper",
                input_format=input_format,
                output_size=len(audio_data),
                target_sample_rate=target_sample_rate
            )
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error preprocessing audio for Whisper: {e}")
            return audio_data  # Return original if preprocessing fails
    
    async def transcribe_twilio_audio(
        self, 
        mulaw_data: bytes,
        **kwargs: Any
    ) -> str:
        """
        Transcribe audio data from Twilio (mulaw format).
        
        Args:
            mulaw_data: Audio data in mulaw format from Twilio
            **kwargs: Additional transcription parameters
            
        Returns:
            Transcribed text
        """
        try:
            # Convert mulaw to WAV for better Whisper compatibility
            wav_data = convert_audio_format(mulaw_data, "mulaw", "wav", sample_rate=8000)
            
            # Transcribe the converted audio
            return await self.transcribe_audio(wav_data, format="wav", **kwargs)
            
        except Exception as e:
            self.logger.error(f"Error transcribing Twilio audio: {e}")
            raise


# Factory function to create STT service based on configuration
def create_stt_service(provider: str = "elevenlabs") -> BaseSTTService:
    """
    Create an STT service instance based on the provider.

    Args:
        provider: STT provider name ("elevenlabs", etc.)

    Returns:
        STT service instance
    """
    if provider.lower() == "elevenlabs":
        config = get_elevenlabs_stt_config()
        return ElevenLabsSTTService(config)
    else:
        raise ValueError(f"Unsupported STT provider: {provider}")


# Global STT service instance
stt_service = create_stt_service("elevenlabs")
