"""
Configuration management for the FastAPI chatbot application.

This module handles environment variable loading and validation using Pydantic settings.
All configuration is centralized here for easy management and type safety.
"""

import os
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from pydantic_settings import BaseSettings as PydanticBaseSettings


class Settings(PydanticBaseSettings):
    """Application settings loaded from environment variables."""
    
    # =============================================================================
    # SERVER CONFIGURATION
    # =============================================================================
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=5050, env="PORT")
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # =============================================================================
    # TWILIO CONFIGURATION
    # =============================================================================
    twilio_account_sid: str = Field(..., env="TWILIO_ACCOUNT_SID")
    twilio_auth_token: str = Field(..., env="TWILIO_AUTH_TOKEN")
    twilio_phone_number: str = Field(..., env="TWILIO_PHONE_NUMBER")
    twilio_validate_webhooks: bool = Field(default=False, env="TWILIO_VALIDATE_WEBHOOKS")
    
    # =============================================================================
    # ELEVENLABS CONFIGURATION
    # =============================================================================
    elevenlabs_api_key: str = Field(..., env="ELEVENLABS_API_KEY")
    elevenlabs_voice_id: str = Field(default="21m00Tcm4TlvDq8ikWAM", env="ELEVENLABS_VOICE_ID")
    elevenlabs_model_id: str = Field(default="eleven_monolingual_v1", env="ELEVENLABS_MODEL_ID")
    elevenlabs_stability: float = Field(default=0.5, env="ELEVENLABS_STABILITY")
    elevenlabs_similarity_boost: float = Field(default=0.8, env="ELEVENLABS_SIMILARITY_BOOST")
    elevenlabs_style: float = Field(default=0.0, env="ELEVENLABS_STYLE")
    elevenlabs_use_speaker_boost: bool = Field(default=True, env="ELEVENLABS_USE_SPEAKER_BOOST")
    
    # =============================================================================
    # GROQ CONFIGURATION
    # =============================================================================
    groq_api_key: str = Field(..., env="GROQ_API_KEY")
    groq_model: str = Field(default="llama3-8b-8192", env="GROQ_MODEL")
    groq_temperature: float = Field(default=0.7, env="GROQ_TEMPERATURE")
    groq_max_tokens: int = Field(default=1024, env="GROQ_MAX_TOKENS")

    # =============================================================================
    # ELEVENLABS STT CONFIGURATION
    # =============================================================================
    elevenlabs_stt_model: str = Field(default="eleven_multilingual_v2", env="ELEVENLABS_STT_MODEL")
    
    # =============================================================================
    # CHATBOT CONFIGURATION
    # =============================================================================
    chatbot_system_message: str = Field(
        default="You are a helpful AI assistant. Keep responses concise and friendly.",
        env="CHATBOT_SYSTEM_MESSAGE"
    )
    max_conversation_history: int = Field(default=10, env="MAX_CONVERSATION_HISTORY")
    session_timeout_minutes: int = Field(default=30, env="SESSION_TIMEOUT_MINUTES")
    
    # =============================================================================
    # AUDIO CONFIGURATION
    # =============================================================================
    twilio_audio_format: str = Field(default="mulaw", env="TWILIO_AUDIO_FORMAT")
    audio_sample_rate: int = Field(default=8000, env="AUDIO_SAMPLE_RATE")
    audio_chunk_size: int = Field(default=1024, env="AUDIO_CHUNK_SIZE")
    
    # =============================================================================
    # OPTIONAL SERVICES
    # =============================================================================
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # =============================================================================
    # SECURITY CONFIGURATION
    # =============================================================================
    secret_key: str = Field(..., env="SECRET_KEY")
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:8000",
        env="CORS_ORIGINS"
    )
    
    # =============================================================================
    # MONITORING AND LOGGING
    # =============================================================================
    enable_request_logging: bool = Field(default=True, env="ENABLE_REQUEST_LOGGING")
    enable_monitoring: bool = Field(default=True, env="ENABLE_MONITORING")
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    
    # =============================================================================
    # WEBHOOK CONFIGURATION
    # =============================================================================
    webhook_base_url: Optional[str] = Field(default=None, env="WEBHOOK_BASE_URL")
    
    # =============================================================================
    # FEATURE FLAGS
    # =============================================================================
    enable_sms: bool = Field(default=True, env="ENABLE_SMS")
    enable_voice: bool = Field(default=True, env="ENABLE_VOICE")
    enable_voice_streaming: bool = Field(default=True, env="ENABLE_VOICE_STREAMING")
    enable_session_persistence: bool = Field(default=False, env="ENABLE_SESSION_PERSISTENCE")
    enable_twilio_init: bool = Field(default=True, env="ENABLE_TWILIO_INIT")
    
    def get_cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list."""
        return [origin.strip() for origin in self.cors_origins.split(",")]
    
    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    @field_validator("twilio_phone_number")
    @classmethod
    def validate_phone_number(cls, v):
        """Validate Twilio phone number format."""
        if not v.startswith("+"):
            raise ValueError("Twilio phone number must start with '+'")
        return v
    
    @field_validator("elevenlabs_stability", "elevenlabs_similarity_boost", "elevenlabs_style")
    @classmethod
    def validate_elevenlabs_params(cls, v):
        """Validate ElevenLabs parameters are between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("ElevenLabs parameters must be between 0 and 1")
        return v
    
    @field_validator("groq_temperature")
    @classmethod
    def validate_groq_temperature(cls, v):
        """Validate Groq temperature parameter."""
        if not 0 <= v <= 2:
            raise ValueError("Groq temperature must be between 0 and 2")
        return v

    @field_validator("groq_max_tokens")
    @classmethod
    def validate_groq_max_tokens(cls, v):
        """Validate Groq max tokens parameter."""
        if not 1 <= v <= 8192:
            raise ValueError("Groq max tokens must be between 1 and 8192")
        return v
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


# Convenience functions for common configuration groups
def get_twilio_config() -> dict:
    """Get Twilio configuration as a dictionary."""
    return {
        "account_sid": settings.twilio_account_sid,
        "auth_token": settings.twilio_auth_token,
        "phone_number": settings.twilio_phone_number,
        "validate_webhooks": settings.twilio_validate_webhooks,
    }


def get_elevenlabs_config() -> dict:
    """Get ElevenLabs configuration as a dictionary."""
    return {
        "api_key": settings.elevenlabs_api_key,
        "voice_id": settings.elevenlabs_voice_id,
        "model_id": settings.elevenlabs_model_id,
        "stability": settings.elevenlabs_stability,
        "similarity_boost": settings.elevenlabs_similarity_boost,
        "style": settings.elevenlabs_style,
        "use_speaker_boost": settings.elevenlabs_use_speaker_boost,
    }


def get_groq_config() -> dict:
    """Get Groq configuration as a dictionary."""
    return {
        "api_key": settings.groq_api_key,
        "model": settings.groq_model,
        "temperature": settings.groq_temperature,
        "max_tokens": settings.groq_max_tokens,
    }


def get_elevenlabs_stt_config() -> dict:
    """Get ElevenLabs STT configuration as a dictionary."""
    return {
        "api_key": settings.elevenlabs_api_key,
        "model": settings.elevenlabs_stt_model,
    }


def get_audio_config() -> dict:
    """Get audio configuration as a dictionary."""
    return {
        "format": settings.twilio_audio_format,
        "sample_rate": settings.audio_sample_rate,
        "chunk_size": settings.audio_chunk_size,
    }
