# FastAPI Chatbot Usage Examples

This document provides comprehensive examples of how to use and extend the FastAPI Chatbot application.

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [SMS Integration](#sms-integration)
3. [Voice Integration](#voice-integration)
4. [WebSocket Streaming](#websocket-streaming)
5. [Custom Services](#custom-services)
6. [Testing Examples](#testing-examples)
7. [Deployment Examples](#deployment-examples)

## Basic Usage

### Health Check

```bash
# Check if the application is running
curl http://localhost:8000/health

# Response:
{
  "status": "healthy",
  "environment": "development",
  "version": "1.0.0"
}
```

### Readiness Check

```bash
# Check if all services are ready
curl http://localhost:8000/ready

# Response:
{
  "status": "ready",
  "checks": {
    "database": "ok",
    "redis": "ok",
    "twilio": "ok"
  }
}
```

### API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

## SMS Integration

### Setting up Twilio Webhook

1. **Configure your Twilio phone number webhook URL**:
   ```
   https://your-domain.com/sms-webhook
   ```

2. **Test SMS webhook locally with ngrok**:
   ```bash
   # Install ngrok
   npm install -g ngrok
   
   # Expose local server
   ngrok http 8000
   
   # Use the ngrok URL in Twilio console
   https://abc123.ngrok.io/sms-webhook
   ```

### Example SMS Flow

```python
# Example of how SMS processing works internally

from app.services.chatbot import ChatbotService
from app.services.twilio_service import TwilioService

# Initialize services
chatbot = ChatbotService({})
twilio = TwilioService({
    "account_sid": "your_account_sid",
    "auth_token": "your_auth_token",
    "phone_number": "+**********"
})

# Process incoming SMS
async def handle_sms(from_number: str, message: str):
    # Generate session ID
    session_id = f"sms_{from_number.replace('+', '')}"
    
    # Process through chatbot
    response = await chatbot.process_message(
        session_id=session_id,
        message=message,
        channel="sms"
    )
    
    # Send response via Twilio
    await twilio.send_message(
        to=from_number,
        message=response
    )
```

### Testing SMS Webhook

```python
import requests

# Test SMS webhook endpoint
webhook_data = {
    "MessageSid": "SM**********abcdef**********abcdef",
    "AccountSid": "AC**********abcdef**********abcdef",
    "From": "+**********",
    "To": "+**********",
    "Body": "Hello, AI assistant!",
    "NumMedia": "0"
}

response = requests.post(
    "http://localhost:8000/sms-webhook",
    data=webhook_data,
    headers={"Content-Type": "application/x-www-form-urlencoded"}
)

print(response.text)  # TwiML response
```

## Voice Integration

### Setting up Voice Webhook

1. **Configure your Twilio phone number voice webhook URL**:
   ```
   https://your-domain.com/voice-webhook
   ```

2. **Enable Media Streams for real-time audio**:
   ```xml
   <!-- TwiML response for streaming -->
   <Response>
     <Connect>
       <Stream url="wss://your-domain.com/media-stream" />
     </Connect>
   </Response>
   ```

### Example Voice Flow

```python
from app.services.session import SessionService
from app.models.session import ChannelType, CallStatus

# Initialize session service
session_service = SessionService({})

# Handle incoming voice call
async def handle_voice_call(call_sid: str, from_number: str, to_number: str):
    # Create session
    session_id = f"voice_{call_sid}"
    
    session = await session_service.create_session(
        session_id=session_id,
        channel=ChannelType.VOICE,
        user_phone=from_number,
        bot_phone=to_number
    )
    
    # Create call session
    call_session = await session_service.create_voice_call_session(
        call_sid=call_sid,
        session_id=session_id,
        from_number=from_number,
        to_number=to_number,
        direction="inbound",
        status=CallStatus.IN_PROGRESS
    )
    
    return call_session
```

## WebSocket Streaming

### Media Stream Connection

```javascript
// Example client-side WebSocket connection (for testing)
const ws = new WebSocket('wss://your-domain.com/media-stream');

ws.onopen = function(event) {
    console.log('WebSocket connected');
    
    // Send start message (simulating Twilio)
    ws.send(JSON.stringify({
        event: 'start',
        start: {
            streamSid: 'MZ123456789',
            callSid: 'CA123456789',
            accountSid: 'AC123456789'
        }
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
    
    if (data.event === 'media') {
        // Handle audio data
        const audioData = atob(data.media.payload);
        console.log('Audio data length:', audioData.length);
    }
};

// Send audio data
function sendAudio(audioData) {
    const payload = btoa(audioData);
    ws.send(JSON.stringify({
        event: 'media',
        streamSid: 'MZ123456789',
        media: {
            payload: payload
        }
    }));
}
```

### Real-time Audio Processing

```python
from app.services.stt import stt_service
from app.services.tts import tts_service
from app.services.groq_service import groq_service

# Example audio processing pipeline
async def process_audio_stream(audio_data: bytes):
    # Convert audio to text using ElevenLabs STT
    text = await stt_service.transcribe_twilio_audio(audio_data)

    if text:
        # Process through chatbot (which uses Groq LLM)
        response = await chatbot.process_message(
            session_id="stream_session",
            message=text,
            channel="voice"
        )

        # Convert response to audio using ElevenLabs TTS
        audio_response = await tts_service.synthesize_speech(response)

        return audio_response

    return None
```

## Custom Services

### Creating a Custom TTS Service

```python
from app.services.base import BaseTTSService, ServiceHealth, ServiceStatus
from typing import Any, Dict, AsyncIterator

class CustomTTSService(BaseTTSService):
    def __init__(self, config: Dict[str, Any]):
        super().__init__("custom_tts", config)
        self.api_key = config["api_key"]
    
    async def initialize(self) -> None:
        await super().initialize()
        # Initialize your TTS service
        self.logger.info("Custom TTS service initialized")
    
    async def health_check(self) -> ServiceHealth:
        return ServiceHealth(
            status=ServiceStatus.HEALTHY,
            message="Custom TTS service is operational"
        )
    
    async def synthesize_speech(
        self, 
        text: str, 
        voice_id: str = None,
        **kwargs: Any
    ) -> bytes:
        # Implement your TTS logic
        self.logger.info(f"Synthesizing: {text[:50]}...")
        
        # Return audio data
        return b"your_audio_data_here"
    
    async def stream_speech(
        self, 
        text: str, 
        voice_id: str = None,
        **kwargs: Any
    ) -> AsyncIterator[bytes]:
        # Implement streaming TTS
        chunks = self._split_text_into_chunks(text)
        
        for chunk in chunks:
            audio_data = await self.synthesize_speech(chunk)
            yield audio_data
```

### Using Groq Service Directly

```python
from app.services.groq_service import groq_service

# Direct Groq usage example
async def generate_custom_response(user_message: str):
    # Ensure service is initialized
    if not groq_service.is_initialized():
        await groq_service.initialize()

    # Generate response with custom parameters
    response = await groq_service.generate_response(
        messages=[{"role": "user", "content": user_message}],
        system_message="You are a helpful customer service assistant.",
        temperature=0.5,
        max_tokens=256
    )

    return response

# Streaming response example
async def generate_streaming_response(user_message: str):
    async for chunk in groq_service.generate_streaming_response(
        messages=[{"role": "user", "content": user_message}],
        system_message="You are a creative writing assistant.",
        temperature=0.8
    ):
        print(chunk, end="", flush=True)
```

### Custom Chatbot Logic

```python
from app.services.chatbot import ChatbotService
from app.services.groq_service import groq_service

class CustomChatbotService(ChatbotService):
    async def _generate_response(
        self,
        context,
        message: str,
        channel: str
    ) -> str:
        # Custom logic with Groq integration
        if "weather" in message.lower():
            # Use Groq for weather-related responses
            weather_response = await groq_service.generate_simple_response(
                f"Provide weather information for: {message}",
                system_message="You are a weather assistant. Provide helpful weather information."
            )
            return weather_response
        elif "appointment" in message.lower():
            return "Let me help you schedule an appointment."
        else:
            # Use default Groq response
            return await super()._generate_response(context, message, channel)
```

## Testing Examples

### Unit Test Example

```python
import pytest
from unittest.mock import AsyncMock, patch
from app.services.chatbot import ChatbotService

class TestCustomChatbot:
    @pytest.fixture
    def chatbot_service(self):
        return ChatbotService({})
    
    @pytest.mark.asyncio
    async def test_weather_query(self, chatbot_service):
        await chatbot_service.initialize()
        
        response = await chatbot_service.process_message(
            session_id="test_session",
            message="What's the weather like?",
            channel="sms"
        )
        
        assert "weather" in response.lower()
    
    @pytest.mark.asyncio
    async def test_conversation_context(self, chatbot_service):
        await chatbot_service.initialize()
        
        # First message
        await chatbot_service.process_message(
            session_id="context_test",
            message="My name is John",
            channel="sms"
        )
        
        # Second message should remember context
        response = await chatbot_service.process_message(
            session_id="context_test",
            message="What's my name?",
            channel="sms"
        )
        
        assert "john" in response.lower()
```

### Integration Test Example

```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

@pytest.mark.integration
class TestSMSIntegration:
    def test_complete_sms_flow(self):
        client = TestClient(app)
        
        # Simulate Twilio SMS webhook
        webhook_data = {
            "MessageSid": "SM123",
            "From": "+**********",
            "To": "+**********",
            "Body": "Hello AI"
        }
        
        response = client.post("/sms-webhook", data=webhook_data)
        
        assert response.status_code == 200
        assert "Message" in response.text
        assert response.headers["content-type"] == "text/xml; charset=utf-8"
```

## Deployment Examples

### Docker Compose for Development

```yaml
version: '3.8'
services:
  chatbot-dev:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - ./app:/app/app
      - ./logs:/app/logs
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fastapi-chatbot
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fastapi-chatbot
  template:
    metadata:
      labels:
        app: fastapi-chatbot
    spec:
      containers:
      - name: chatbot
        image: your-registry/fastapi-chatbot:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: TWILIO_ACCOUNT_SID
          valueFrom:
            secretKeyRef:
              name: twilio-secrets
              key: account-sid
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Environment-specific Configuration

```python
# config/production.py
from app.config import Settings

class ProductionSettings(Settings):
    environment: str = "production"
    debug: bool = False
    log_level: str = "info"
    
    # Production-specific overrides
    session_timeout_minutes: int = 60
    max_sessions: int = 50000
    
    # Security settings
    cors_origins: list = ["https://your-domain.com"]
    enable_request_logging: bool = True

# Usage in main.py
import os
from config.production import ProductionSettings

if os.getenv("ENVIRONMENT") == "production":
    settings = ProductionSettings()
```

## Monitoring and Observability

### Custom Metrics

```python
from app.services.monitoring import monitoring_service

# Record custom metrics
monitoring_service.record_request(response_time_ms=150, success=True)

# Get system metrics
metrics = await monitoring_service.get_system_metrics()
print(f"Active sessions: {metrics['sessions']['active']}")
```

### Health Check Integration

```python
from app.services.base import ServiceHealth, ServiceStatus

class CustomHealthCheck:
    async def check_external_service(self) -> ServiceHealth:
        try:
            # Check your external service
            response = await external_service.ping()
            
            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="External service is responsive",
                response_time_ms=response.time
            )
        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"External service error: {str(e)}"
            )
```

This completes the comprehensive examples documentation. The examples cover all major aspects of the application including basic usage, integrations, custom extensions, testing, and deployment scenarios.
