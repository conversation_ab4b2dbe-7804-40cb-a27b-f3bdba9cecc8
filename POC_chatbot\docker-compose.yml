# Docker Compose configuration for FastAPI Chatbot
# Includes the main application, Redis for session storage, and PostgreSQL for data persistence

version: '3.8'

services:
  # Main FastAPI application
  chatbot-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: fastapi-chatbot
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Server configuration
      - HOST=0.0.0.0
      - PORT=8000
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=info
      
      # Database configuration
      - DATABASE_URL=***************************************************/chatbot_db
      - REDIS_URL=redis://redis:6379/0
      
      # Feature flags
      - ENABLE_SMS=true
      - ENABLE_VOICE=true
      - ENABLE_VOICE_STREAMING=true
      - ENABLE_WEBHOOKS=true
      - ENABLE_REQUEST_LOGGING=true
      
      # Session configuration
      - SESSION_TIMEOUT_MINUTES=30
      - MAX_SESSIONS=10000
      
      # CORS configuration
      - CORS_ORIGINS=*
      
      # Security configuration
      - SECRET_KEY=${SECRET_KEY}

      # External service configuration (set these in .env file)
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER}
      - TWILIO_VALIDATE_WEBHOOKS=true

      # Groq LLM configuration
      - GROQ_API_KEY=${GROQ_API_KEY}
      - GROQ_MODEL=${GROQ_MODEL:-llama-3.3-70b-versatile}
      - GROQ_TEMPERATURE=${GROQ_TEMPERATURE:-0.7}
      - GROQ_MAX_TOKENS=${GROQ_MAX_TOKENS:-1024}

      # ElevenLabs TTS configuration
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ELEVENLABS_VOICE_ID=${ELEVENLABS_VOICE_ID}
      - ELEVENLABS_MODEL_ID=eleven_monolingual_v1
      - ELEVENLABS_STABILITY=${ELEVENLABS_STABILITY:-0.5}
      - ELEVENLABS_SIMILARITY_BOOST=${ELEVENLABS_SIMILARITY_BOOST:-0.5}
      - ELEVENLABS_STYLE=${ELEVENLABS_STYLE:-0.0}

      # ElevenLabs STT configuration
      - ELEVENLABS_STT_MODEL=${ELEVENLABS_STT_MODEL:-eleven_multilingual_v2}
      
    volumes:
      - ./logs:/app/logs
      - ./static/audio:/app/static/audio
    depends_on:
      - postgres
      - redis
    networks:
      - chatbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database for persistent data
  postgres:
    image: postgres:15-alpine
    container_name: chatbot-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=chatbot_db
      - POSTGRES_USER=chatbot
      - POSTGRES_PASSWORD=chatbot_password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - chatbot-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chatbot -d chatbot_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: chatbot-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - chatbot-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: chatbot-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - chatbot-api
    networks:
      - chatbot-network
    profiles:
      - production

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: chatbot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - chatbot-network
    profiles:
      - monitoring

  # Grafana for metrics visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: chatbot-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - chatbot-network
    profiles:
      - monitoring

# Named volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Network for service communication
networks:
  chatbot-network:
    driver: bridge
