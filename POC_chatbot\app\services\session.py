"""
Session management service for the FastAPI chatbot application.

This service handles conversation sessions, call tracking, and state
management across different communication channels.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from app.services.base import BaseService, ServiceHealth, ServiceStatus
from app.models.session import (
    ConversationSession,
    VoiceCallSession,
    SessionMessage,
    SessionSummary,
    SessionMetrics,
    ChannelType,
    SessionStatus,
    CallStatus,
    MessageRole
)
from app.config import settings
from app.utils.logging import get_logger

logger = get_logger(__name__)


class SessionService(BaseService):
    """
    Service for managing conversation sessions and call tracking.
    
    This service provides session management capabilities including:
    - Session creation and lifecycle management
    - Voice call tracking and logging
    - Message history and context management
    - Session metrics and analytics
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("session", config)
        
        # In-memory storage (in production, use Redis or database)
        self.sessions: Dict[str, ConversationSession] = {}
        self.call_sessions: Dict[str, VoiceCallSession] = {}
        
        # Session configuration
        self.default_timeout_minutes = settings.session_timeout_minutes
        self.max_sessions = config.get("max_sessions", 10000)
        self.cleanup_interval_minutes = config.get("cleanup_interval", 5)
        
        # Metrics tracking
        self.session_metrics = {
            "total_created": 0,
            "total_expired": 0,
            "total_messages": 0,
            "total_calls": 0,
        }
    
    async def initialize(self) -> None:
        """Initialize the session service."""
        await super().initialize()
        
        # Start background cleanup task
        asyncio.create_task(self._cleanup_expired_sessions())
        
        logger.info(
            "Session service initialized",
            max_sessions=self.max_sessions,
            default_timeout=self.default_timeout_minutes
        )
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the session service."""
        try:
            active_sessions = len([s for s in self.sessions.values() if s.status == SessionStatus.ACTIVE])
            active_calls = len([c for c in self.call_sessions.values() if c.is_active()])
            
            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="Session service is operational",
                details={
                    "total_sessions": len(self.sessions),
                    "active_sessions": active_sessions,
                    "active_calls": active_calls,
                    "metrics": self.session_metrics,
                }
            )
        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"Session service health check failed: {str(e)}"
            )
    
    async def create_session(
        self,
        session_id: Optional[str] = None,
        channel: ChannelType = ChannelType.API,
        user_phone: Optional[str] = None,
        bot_phone: Optional[str] = None,
        user_metadata: Optional[Dict[str, Any]] = None,
        timeout_minutes: Optional[int] = None
    ) -> ConversationSession:
        """
        Create a new conversation session.
        
        Args:
            session_id: Custom session ID (generated if not provided)
            channel: Communication channel
            user_phone: User's phone number
            bot_phone: Bot's phone number
            user_metadata: Additional user metadata
            timeout_minutes: Session timeout in minutes
            
        Returns:
            Created conversation session
        """
        if not session_id:
            session_id = self._generate_session_id()
        
        # Check if session already exists
        if session_id in self.sessions:
            logger.warning(f"Session {session_id} already exists, returning existing session")
            return self.sessions[session_id]
        
        # Create new session
        session = ConversationSession(
            session_id=session_id,
            channel=channel,
            user_phone=user_phone,
            bot_phone=bot_phone,
            user_metadata=user_metadata or {}
        )
        
        # Set expiration
        timeout = timeout_minutes or self.default_timeout_minutes
        session.set_expiration(timeout)
        
        # Store session
        self.sessions[session_id] = session
        self.session_metrics["total_created"] += 1
        
        logger.info(
            "Session created",
            session_id=session_id,
            channel=channel.value,
            user_phone=user_phone,
            timeout_minutes=timeout
        )
        
        return session
    
    async def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """
        Get a session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session if found, None otherwise
        """
        session = self.sessions.get(session_id)
        
        if session and session.is_expired():
            await self.expire_session(session_id)
            return None
        
        return session
    
    async def update_session_activity(self, session_id: str) -> bool:
        """
        Update session activity timestamp.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if updated, False if session not found
        """
        session = await self.get_session(session_id)
        if session:
            session.update_activity()
            logger.debug(f"Updated activity for session {session_id}")
            return True
        return False
    
    async def add_message_to_session(
        self,
        session_id: str,
        content: str,
        role: MessageRole,
        channel: ChannelType,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add a message to a session.
        
        Args:
            session_id: Session identifier
            content: Message content
            role: Message role
            channel: Communication channel
            metadata: Additional message metadata
            
        Returns:
            True if added, False if session not found
        """
        session = await self.get_session(session_id)
        if not session:
            return False
        
        message = SessionMessage(
            id=str(uuid.uuid4()),
            content=content,
            role=role,
            channel=channel,
            metadata=metadata or {}
        )
        
        session.add_message(message)
        self.session_metrics["total_messages"] += 1
        
        logger.debug(
            "Message added to session",
            session_id=session_id,
            role=role.value,
            channel=channel.value,
            content_length=len(content)
        )
        
        return True
    
    async def create_voice_call_session(
        self,
        call_sid: str,
        session_id: str,
        from_number: str,
        to_number: str,
        direction: str,
        status: CallStatus = CallStatus.INITIATED,
        **kwargs: Any
    ) -> VoiceCallSession:
        """
        Create a voice call session.
        
        Args:
            call_sid: Twilio call SID
            session_id: Associated conversation session ID
            from_number: Caller's phone number
            to_number: Called phone number
            direction: Call direction
            status: Initial call status
            **kwargs: Additional call metadata
            
        Returns:
            Created voice call session
        """
        call_session = VoiceCallSession(
            call_sid=call_sid,
            session_id=session_id,
            from_number=from_number,
            to_number=to_number,
            status=status,
            direction=direction,
            **kwargs
        )
        
        # Store call session
        self.call_sessions[call_sid] = call_session
        self.session_metrics["total_calls"] += 1
        
        # Update associated conversation session
        session = await self.get_session(session_id)
        if session:
            session.voice_call = call_session
        
        logger.info(
            "Voice call session created",
            call_sid=call_sid,
            session_id=session_id,
            from_number=from_number,
            direction=direction,
            status=status.value
        )
        
        return call_session
    
    async def update_call_status(
        self,
        call_sid: str,
        status: CallStatus,
        **kwargs: Any
    ) -> bool:
        """
        Update voice call status.
        
        Args:
            call_sid: Twilio call SID
            status: New call status
            **kwargs: Additional status data
            
        Returns:
            True if updated, False if call not found
        """
        call_session = self.call_sessions.get(call_sid)
        if not call_session:
            return False
        
        old_status = call_session.status
        call_session.status = status
        
        # Update timestamps based on status
        if status == CallStatus.IN_PROGRESS and not call_session.answered_at:
            call_session.answered_at = datetime.now()
        elif status in [CallStatus.COMPLETED, CallStatus.FAILED, CallStatus.NO_ANSWER]:
            call_session.ended_at = datetime.now()
            if call_session.answered_at:
                call_session.duration = call_session.get_duration_seconds()
        
        # Update additional fields
        for key, value in kwargs.items():
            if hasattr(call_session, key):
                setattr(call_session, key, value)
        
        logger.info(
            "Call status updated",
            call_sid=call_sid,
            old_status=old_status.value,
            new_status=status.value,
            duration=call_session.duration
        )
        
        return True
    
    async def get_call_session(self, call_sid: str) -> Optional[VoiceCallSession]:
        """
        Get a voice call session by call SID.
        
        Args:
            call_sid: Twilio call SID
            
        Returns:
            Call session if found, None otherwise
        """
        return self.call_sessions.get(call_sid)
    
    async def expire_session(self, session_id: str) -> bool:
        """
        Expire a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if expired, False if session not found
        """
        session = self.sessions.get(session_id)
        if not session:
            return False
        
        session.status = SessionStatus.EXPIRED
        session.total_duration = session.get_session_duration()
        
        self.session_metrics["total_expired"] += 1
        
        logger.info(
            "Session expired",
            session_id=session_id,
            duration=session.total_duration,
            message_count=session.message_count
        )
        
        return True
    
    async def get_session_metrics(self) -> SessionMetrics:
        """
        Get session metrics and analytics.
        
        Returns:
            Session metrics object
        """
        active_sessions = [s for s in self.sessions.values() if s.status == SessionStatus.ACTIVE]
        
        # Channel breakdown
        channel_counts = defaultdict(int)
        for session in self.sessions.values():
            channel_counts[session.channel] += 1
        
        # Call metrics
        completed_calls = [c for c in self.call_sessions.values() if c.status == CallStatus.COMPLETED]
        avg_call_duration = 0.0
        if completed_calls:
            total_duration = sum(c.get_duration_seconds() or 0 for c in completed_calls)
            avg_call_duration = total_duration / len(completed_calls)
        
        # Session averages
        avg_duration = 0.0
        avg_messages = 0.0
        if self.sessions:
            total_duration = sum(s.get_session_duration() for s in self.sessions.values())
            total_messages = sum(s.message_count for s in self.sessions.values())
            avg_duration = total_duration / len(self.sessions)
            avg_messages = total_messages / len(self.sessions)
        
        return SessionMetrics(
            total_sessions=len(self.sessions),
            active_sessions=len(active_sessions),
            sms_sessions=channel_counts[ChannelType.SMS],
            voice_sessions=channel_counts[ChannelType.VOICE],
            api_sessions=channel_counts[ChannelType.API],
            average_duration=avg_duration,
            average_messages=avg_messages,
            total_calls=len(self.call_sessions),
            completed_calls=len(completed_calls),
            average_call_duration=avg_call_duration
        )
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return f"session_{uuid.uuid4().hex[:16]}"
    
    async def _cleanup_expired_sessions(self) -> None:
        """Background task to clean up expired sessions."""
        while True:
            try:
                expired_sessions = []
                
                for session_id, session in self.sessions.items():
                    if session.is_expired():
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    await self.expire_session(session_id)
                    # Remove from memory after expiration
                    del self.sessions[session_id]
                
                if expired_sessions:
                    logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
                # Sleep until next cleanup
                await asyncio.sleep(self.cleanup_interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error during session cleanup: {e}")
                await asyncio.sleep(60)  # Shorter sleep on error
