#!/usr/bin/env python3
"""
Test Twilio authentication with current credentials
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_twilio_auth():
    """Test Twilio authentication"""
    try:
        from twilio.rest import Client
        
        # Get credentials from .env
        account_sid = os.getenv("TWILIO_ACCOUNT_SID")
        auth_token = os.getenv("TWILIO_AUTH_TOKEN")
        phone_number = os.getenv("TWILIO_PHONE_NUMBER")
        
        print("🧪 Testing Twilio Authentication")
        print("=" * 50)
        print(f"Account SID: {account_sid}")
        print(f"Auth Token: {'*' * len(auth_token) if auth_token else 'None'}")
        print(f"Phone Number: {phone_number}")
        print()
        
        # Check credential format
        if account_sid.startswith("SK"):
            print("⚠️  Detected API Key SID (starts with SK)")
            print("   This requires special authentication method")
        elif account_sid.startswith("AC"):
            print("✅ Detected Account SID (starts with AC)")
        else:
            print("❌ Unknown credential format")
            return False
        
        print()
        print("🔄 Testing authentication...")
        
        # Try to initialize client
        client = Client(account_sid, auth_token)
        
        # Try to fetch account info to test authentication
        account = client.api.accounts(account_sid).fetch()
        
        print(f"✅ Authentication successful!")
        print(f"Account Name: {account.friendly_name}")
        print(f"Account Status: {account.status}")
        print(f"Account Type: {account.type}")
        
        # Try to list phone numbers to verify phone number access
        print("\n📞 Testing phone number access...")
        phone_numbers = client.incoming_phone_numbers.list(limit=5)
        
        if phone_numbers:
            print(f"✅ Found {len(phone_numbers)} phone number(s)")
            for pn in phone_numbers:
                print(f"   📱 {pn.phone_number} ({pn.friendly_name})")
                if pn.phone_number == phone_number:
                    print(f"   ✅ Your configured number {phone_number} is verified!")
        else:
            print("⚠️  No phone numbers found")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication failed: {str(e)}")
        
        # Provide specific guidance based on error
        error_str = str(e).lower()
        if "authenticate" in error_str:
            print("\n💡 Authentication Error Solutions:")
            print("1. Verify your Account SID and Auth Token in Twilio Console")
            print("2. If using API Key (SK...), make sure you have the correct secret")
            print("3. Check if your credentials have expired")
        elif "not found" in error_str:
            print("\n💡 Account Not Found Solutions:")
            print("1. Double-check your Account SID in Twilio Console")
            print("2. Make sure you're using the right Twilio account")
        
        return False

if __name__ == "__main__":
    success = test_twilio_auth()
    if success:
        print("\n🎉 Twilio authentication test passed!")
        print("You should be able to send SMS and make calls now.")
    else:
        print("\n💥 Twilio authentication test failed!")
        print("Please check your credentials in the .env file.")
