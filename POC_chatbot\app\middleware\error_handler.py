"""
Global error handling middleware for the FastAPI chatbot application.

This middleware catches unhandled exceptions and returns appropriate
error responses while logging the errors for debugging.
"""

import traceback
from typing import Callable

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logging import get_logger

logger = get_logger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle unhandled exceptions globally.
    
    Catches exceptions, logs them, and returns appropriate error responses
    to clients while preventing sensitive information from being exposed.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and handle any exceptions.
        
        Args:
            request: The incoming HTTP request
            call_next: The next middleware or route handler
            
        Returns:
            HTTP response, either from the handler or error response
        """
        try:
            response = await call_next(request)
            return response
            
        except Exception as exc:
            return await self._handle_exception(request, exc)
    
    async def _handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """
        Handle an unhandled exception.
        
        Args:
            request: The HTTP request that caused the exception
            exc: The exception that occurred
            
        Returns:
            JSON error response
        """
        # Log the error with context
        logger.error(
            "Unhandled exception occurred",
            error_type=type(exc).__name__,
            error_message=str(exc),
            path=request.url.path,
            method=request.method,
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            traceback=traceback.format_exc(),
        )
        
        # Determine error response based on exception type
        if isinstance(exc, ValueError):
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": "Bad Request",
                    "message": "Invalid input provided",
                    "type": "validation_error"
                }
            )
        
        elif isinstance(exc, PermissionError):
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": "Forbidden",
                    "message": "Access denied",
                    "type": "permission_error"
                }
            )
        
        elif isinstance(exc, FileNotFoundError):
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "error": "Not Found",
                    "message": "Resource not found",
                    "type": "not_found_error"
                }
            )
        
        elif isinstance(exc, TimeoutError):
            return JSONResponse(
                status_code=status.HTTP_408_REQUEST_TIMEOUT,
                content={
                    "error": "Request Timeout",
                    "message": "Request timed out",
                    "type": "timeout_error"
                }
            )
        
        else:
            # Generic internal server error for unknown exceptions
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "Internal Server Error",
                    "message": "An unexpected error occurred",
                    "type": "internal_error"
                }
            )


class TwilioWebhookError(Exception):
    """Custom exception for Twilio webhook validation errors."""
    pass


class ExternalServiceError(Exception):
    """Custom exception for external service integration errors."""
    pass


class AudioProcessingError(Exception):
    """Custom exception for audio processing errors."""
    pass


class SessionError(Exception):
    """Custom exception for session management errors."""
    pass
