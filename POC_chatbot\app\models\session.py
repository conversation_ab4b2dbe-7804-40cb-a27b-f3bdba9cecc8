"""
Session and conversation state models for the FastAPI chatbot application.

This module contains data models for managing conversation sessions,
call tracking, and state persistence across different channels.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from enum import Enum
from pydantic import BaseModel, Field
from dataclasses import dataclass


class ChannelType(str, Enum):
    """Communication channel types."""
    SMS = "sms"
    VOICE = "voice"
    API = "api"
    WEBSOCKET = "websocket"


class SessionStatus(str, Enum):
    """Session status types."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    TERMINATED = "terminated"


class CallStatus(str, Enum):
    """Voice call status types."""
    INITIATED = "initiated"
    RINGING = "ringing"
    IN_PROGRESS = "in-progress"
    COMPLETED = "completed"
    FAILED = "failed"
    NO_ANSWER = "no-answer"
    BUSY = "busy"
    CANCELED = "canceled"


class MessageRole(str, Enum):
    """Message role types."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class SessionMessage(BaseModel):
    """Model for individual messages within a session."""
    
    id: str = Field(..., description="Unique message identifier")
    content: str = Field(..., description="Message content")
    role: MessageRole = Field(..., description="Message role")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    channel: ChannelType = Field(..., description="Communication channel")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional message metadata")
    
    class Config:
        use_enum_values = True


class VoiceCallSession(BaseModel):
    """Model for voice call session tracking."""
    
    call_sid: str = Field(..., description="Twilio call SID")
    session_id: str = Field(..., description="Internal session ID")
    from_number: str = Field(..., description="Caller's phone number")
    to_number: str = Field(..., description="Called phone number")
    status: CallStatus = Field(..., description="Current call status")
    direction: str = Field(..., description="Call direction (inbound/outbound)")
    
    # Call timing
    created_at: datetime = Field(default_factory=datetime.now, description="Call creation time")
    answered_at: Optional[datetime] = Field(default=None, description="Call answer time")
    ended_at: Optional[datetime] = Field(default=None, description="Call end time")
    duration: Optional[int] = Field(default=None, description="Call duration in seconds")
    
    # Call details
    caller_name: Optional[str] = Field(default=None, description="Caller's name")
    caller_city: Optional[str] = Field(default=None, description="Caller's city")
    caller_state: Optional[str] = Field(default=None, description="Caller's state")
    caller_country: Optional[str] = Field(default=None, description="Caller's country")
    
    # Media streaming
    stream_sid: Optional[str] = Field(default=None, description="Media stream SID")
    streaming_enabled: bool = Field(default=False, description="Whether streaming is enabled")
    
    # Conversation data
    messages: List[SessionMessage] = Field(default_factory=list, description="Call messages")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional call metadata")
    
    class Config:
        use_enum_values = True
    
    def add_message(self, message: SessionMessage) -> None:
        """Add a message to the call session."""
        self.messages.append(message)
    
    def get_duration_seconds(self) -> Optional[int]:
        """Calculate call duration in seconds."""
        if self.answered_at and self.ended_at:
            return int((self.ended_at - self.answered_at).total_seconds())
        return self.duration
    
    def is_active(self) -> bool:
        """Check if the call is currently active."""
        return self.status in [CallStatus.RINGING, CallStatus.IN_PROGRESS]


class ConversationSession(BaseModel):
    """Model for conversation session across all channels."""
    
    session_id: str = Field(..., description="Unique session identifier")
    channel: ChannelType = Field(..., description="Primary communication channel")
    status: SessionStatus = Field(default=SessionStatus.ACTIVE, description="Session status")
    
    # Participant information
    user_phone: Optional[str] = Field(default=None, description="User's phone number")
    bot_phone: Optional[str] = Field(default=None, description="Bot's phone number")
    user_metadata: Dict[str, Any] = Field(default_factory=dict, description="User metadata")
    
    # Session timing
    created_at: datetime = Field(default_factory=datetime.now, description="Session creation time")
    last_activity: datetime = Field(default_factory=datetime.now, description="Last activity time")
    expires_at: Optional[datetime] = Field(default=None, description="Session expiration time")
    
    # Conversation data
    messages: List[SessionMessage] = Field(default_factory=list, description="Session messages")
    context: Dict[str, Any] = Field(default_factory=dict, description="Conversation context")
    
    # Channel-specific data
    sms_data: Optional[Dict[str, Any]] = Field(default=None, description="SMS-specific data")
    voice_call: Optional[VoiceCallSession] = Field(default=None, description="Voice call data")
    
    # Statistics
    message_count: int = Field(default=0, description="Total message count")
    total_duration: int = Field(default=0, description="Total session duration in seconds")
    
    class Config:
        use_enum_values = True
    
    def add_message(self, message: SessionMessage) -> None:
        """Add a message to the session."""
        self.messages.append(message)
        self.message_count += 1
        self.last_activity = datetime.now()
        
        # Also add to voice call if applicable
        if self.voice_call and message.channel == ChannelType.VOICE:
            self.voice_call.add_message(message)
    
    def update_activity(self) -> None:
        """Update the last activity timestamp."""
        self.last_activity = datetime.now()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """Check if the session has expired."""
        if self.expires_at:
            return datetime.now() > self.expires_at
        
        timeout = timedelta(minutes=timeout_minutes)
        return datetime.now() - self.last_activity > timeout
    
    def set_expiration(self, minutes: int) -> None:
        """Set session expiration time."""
        self.expires_at = datetime.now() + timedelta(minutes=minutes)
    
    def get_recent_messages(self, count: int = 10) -> List[SessionMessage]:
        """Get recent messages from the session."""
        return self.messages[-count:] if self.messages else []
    
    def get_session_duration(self) -> int:
        """Get total session duration in seconds."""
        if self.status == SessionStatus.ACTIVE:
            return int((datetime.now() - self.created_at).total_seconds())
        return self.total_duration


class SessionSummary(BaseModel):
    """Model for session summary information."""
    
    session_id: str = Field(..., description="Session identifier")
    channel: ChannelType = Field(..., description="Communication channel")
    status: SessionStatus = Field(..., description="Session status")
    
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    duration: int = Field(..., description="Session duration in seconds")
    
    message_count: int = Field(..., description="Total message count")
    user_phone: Optional[str] = Field(default=None, description="User's phone number")
    
    # Call-specific summary
    call_duration: Optional[int] = Field(default=None, description="Call duration in seconds")
    call_status: Optional[CallStatus] = Field(default=None, description="Call status")
    
    class Config:
        use_enum_values = True


class SessionMetrics(BaseModel):
    """Model for session metrics and analytics."""
    
    total_sessions: int = Field(..., description="Total number of sessions")
    active_sessions: int = Field(..., description="Currently active sessions")
    
    # Channel breakdown
    sms_sessions: int = Field(default=0, description="SMS sessions")
    voice_sessions: int = Field(default=0, description="Voice sessions")
    api_sessions: int = Field(default=0, description="API sessions")
    
    # Timing metrics
    average_duration: float = Field(default=0.0, description="Average session duration")
    average_messages: float = Field(default=0.0, description="Average messages per session")
    
    # Call metrics
    total_calls: int = Field(default=0, description="Total voice calls")
    completed_calls: int = Field(default=0, description="Completed calls")
    average_call_duration: float = Field(default=0.0, description="Average call duration")
    
    timestamp: datetime = Field(default_factory=datetime.now, description="Metrics timestamp")
    
    class Config:
        use_enum_values = True
