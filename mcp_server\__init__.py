"""
MCP Server for SmartHR Integration

This module provides Model Context Protocol (MCP) server functionality
for the SmartHR system, offering three main tools:

1. extract_top_candidates - Vectorize and match candidates against job positions
2. generate_interview_questions - Create interview questions with expected responses
3. evaluate_interview - Evaluate interview transcripts using four-agent system

The MCP server follows the Server-Sent Events (SSE) pattern for real-time
communication with MCP clients and integrates with the SmartHR system via HTTP API.
"""

from .server import MCPServer
from .tools import (
    extract_top_candidates,
    generate_interview_questions,
    evaluate_interview
)
from .models import (
    ExtractCandidatesRequest,
    ExtractCandidatesResponse,
    GenerateQuestionsRequest,
    GenerateQuestionsResponse,
    EvaluateInterviewRequest,
    EvaluateInterviewResponse
)

__version__ = "1.0.0"
__all__ = [
    "MCPServer",
    "extract_top_candidates",
    "generate_interview_questions", 
    "evaluate_interview",
    "ExtractCandidatesRequest",
    "ExtractCandidatesResponse",
    "GenerateQuestionsRequest",
    "GenerateQuestionsResponse",
    "EvaluateInterviewRequest",
    "EvaluateInterviewResponse"
]
