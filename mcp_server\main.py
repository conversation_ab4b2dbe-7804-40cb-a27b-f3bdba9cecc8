#!/usr/bin/env python3
"""
Main entry point for the external MCP server.

This script provides command-line interface for running the MCP server
in different modes and configurations.
"""

import sys
import os
import logging
import argparse
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import get_mcp_config, validate_config
from server import get_mcp_server
from smarthr_client import get_smarthr_client


def setup_logging():
    """Setup logging configuration."""
    config = get_mcp_config()
    
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format=config.log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("mcp_server.log")
        ]
    )


def check_dependencies():
    """Check if all required dependencies are available."""
    try:
        import fastapi
        import uvicorn
        import httpx
        import pydantic
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please install required packages: pip install fastapi uvicorn httpx pydantic python-dotenv")
        return False


async def health_check():
    """Perform health check on SmartHR API."""
    print("Checking SmartHR API connectivity...")
    
    smarthr_client = get_smarthr_client()
    if await smarthr_client.health_check():
        print("✓ SmartHR API is accessible")
        return True
    else:
        print("✗ SmartHR API is not accessible")
        print(f"  URL: {smarthr_client.base_url}")
        return False


def run_server():
    """Run the MCP server."""
    print("Starting MCP Server...")
    
    # Setup logging
    setup_logging()
    
    # Validate configuration
    if not validate_config():
        print("Configuration validation failed. Please check your environment variables.")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Get and run server
    server = get_mcp_server()
    server.run()


def run_health_check():
    """Run health check command."""
    import asyncio
    
    setup_logging()
    
    config = get_mcp_config()
    print(f"MCP Server Configuration:")
    print(f"  Host: {config.mcp_server_host}")
    print(f"  Port: {config.mcp_server_port}")
    print(f"  SmartHR API: {config.smarthr_api_url}")
    print()
    
    # Run async health check
    result = asyncio.run(health_check())
    
    if result:
        print("\n✓ All systems operational")
        sys.exit(0)
    else:
        print("\n✗ Health check failed")
        sys.exit(1)


def show_config():
    """Show current configuration."""
    config = get_mcp_config()
    
    print("MCP Server Configuration:")
    print(f"  Server Host: {config.mcp_server_host}")
    print(f"  Server Port: {config.mcp_server_port}")
    print(f"  Debug Mode: {config.mcp_server_debug}")
    print(f"  SmartHR API URL: {config.smarthr_api_url}")
    print(f"  SmartHR API Timeout: {config.smarthr_api_timeout}s")
    print()
    print("Tools Enabled:")
    print(f"  Extract Candidates: {config.enable_extract_candidates}")
    print(f"  Generate Questions: {config.enable_generate_questions}")
    print(f"  Evaluate Interview: {config.enable_evaluate_interview}")
    print()
    print("Limits:")
    print(f"  Max Candidates per Search: {config.max_candidates_per_search}")
    print(f"  Default Questions per Interview: {config.default_questions_per_interview}")
    print(f"  Max Questions per Interview: {config.max_questions_per_interview}")
    print(f"  Vector Similarity Threshold: {config.vector_similarity_threshold}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="SmartHR MCP Server")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Start server command
    start_parser = subparsers.add_parser("start", help="Start the MCP server")
    
    # Health check command
    health_parser = subparsers.add_parser("health", help="Run health check")
    
    # Config command
    config_parser = subparsers.add_parser("config", help="Show configuration")
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.command == "start":
        run_server()
    elif args.command == "health":
        run_health_check()
    elif args.command == "config":
        show_config()
    else:
        # Default to starting server
        run_server()


if __name__ == "__main__":
    main()
