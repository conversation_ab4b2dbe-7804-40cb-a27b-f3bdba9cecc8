"""
Logging configuration for the FastAPI chatbot application.

This module sets up structured logging with appropriate formatters,
handlers, and log levels for different environments.
"""

import logging
import sys
from typing import Any, Dict

import structlog
from app.config import settings


def setup_logging() -> None:
    """
    Configure logging for the application.
    
    Sets up structured logging with JSON formatting for production
    and human-readable formatting for development.
    """
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.environment == "production"
            else structlog.dev.Console<PERSON>enderer(colors=True),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level),
    )
    
    # Set log levels for external libraries
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("twilio").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("websockets").setLevel(logging.INFO)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to other classes.
    """
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get a logger instance for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs: Any) -> None:
    """
    Log a function call with parameters.
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger("function_call")
    logger.info(f"Calling {func_name}", **kwargs)


def log_api_call(service: str, endpoint: str, **kwargs: Any) -> None:
    """
    Log an external API call.
    
    Args:
        service: Name of the external service
        endpoint: API endpoint being called
        **kwargs: Additional context to log
    """
    logger = get_logger("api_call")
    logger.info(f"API call to {service}", endpoint=endpoint, **kwargs)


def log_error(error: Exception, context: Dict[str, Any] = None) -> None:
    """
    Log an error with context.
    
    Args:
        error: Exception that occurred
        context: Additional context information
    """
    logger = get_logger("error")
    logger.error(
        f"Error occurred: {type(error).__name__}",
        error_message=str(error),
        error_type=type(error).__name__,
        context=context or {},
        exc_info=True
    )
