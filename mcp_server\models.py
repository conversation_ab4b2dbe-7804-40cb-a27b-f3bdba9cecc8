"""
Pydantic models for MCP server tools and data structures.

This module defines the request/response models for the three main MCP tools:
- extract_top_candidates
- generate_interview_questions  
- evaluate_interview
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


# ─────────────────────────────── Common Enums ───────────────────────────────────

class SeniorityLevel(str, Enum):
    """Seniority levels for candidates and questions."""
    JUNIOR = "junior"
    MID = "mid"
    SENIOR = "senior"
    NA = "n/a"


class SkillCategory(str, Enum):
    """Categories for skills and questions."""
    TECHNICAL_SKILLS = "Technical Skills"
    SOFT_SKILLS = "Soft Skills"
    METHODOLOGIES = "Methodologies"
    LANGUAGE_TOOLS = "Language - Tools"


# ─────────────────────────── Extract Top Candidates Models ──────────────────────────

class JobPosition(BaseModel):
    """Job position information for candidate matching."""
    position_id: str = Field(..., description="Unique identifier for the position")
    title: str = Field(..., description="Job title")
    description: str = Field(..., description="Job description")
    required_skills: List[str] = Field(default=[], description="List of required skills")
    preferred_skills: List[str] = Field(default=[], description="List of preferred skills")
    seniority_level: SeniorityLevel = Field(default=SeniorityLevel.MID, description="Required seniority level")
    location: Optional[str] = Field(None, description="Job location")
    department: Optional[str] = Field(None, description="Department")


class CandidateMatch(BaseModel):
    """Candidate match result with similarity scores."""
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    name: str = Field(..., description="Candidate name")
    email: Optional[str] = Field(None, description="Candidate email")
    similarity_score: float = Field(..., description="Overall similarity score (0.0 to 1.0)")
    skill_match_score: float = Field(..., description="Skill matching score (0.0 to 1.0)")
    experience_match_score: float = Field(..., description="Experience matching score (0.0 to 1.0)")
    seniority_match: SeniorityLevel = Field(..., description="Detected seniority level")
    matching_skills: List[str] = Field(default=[], description="Skills that match the position")
    missing_skills: List[str] = Field(default=[], description="Required skills the candidate lacks")
    years_of_experience: Optional[float] = Field(None, description="Total years of experience")
    summary: Optional[str] = Field(None, description="Professional summary")


class ExtractCandidatesRequest(BaseModel):
    """Request model for extracting top candidates."""
    position: JobPosition = Field(..., description="Job position to match candidates against")
    max_candidates: int = Field(default=10, description="Maximum number of candidates to return")
    min_similarity_score: float = Field(default=0.3, description="Minimum similarity score threshold")
    include_skills_analysis: bool = Field(default=True, description="Include detailed skills analysis")
    filter_by_seniority: bool = Field(default=False, description="Filter candidates by seniority level")


class ExtractCandidatesResponse(BaseModel):
    """Response model for extracted candidates."""
    success: bool = Field(..., description="Whether the operation was successful")
    candidates: List[CandidateMatch] = Field(default=[], description="List of matched candidates")
    total_candidates_found: int = Field(..., description="Total number of candidates found")
    search_metadata: Dict[str, Any] = Field(default={}, description="Search metadata and statistics")
    error_message: Optional[str] = Field(None, description="Error message if operation failed")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")


# ─────────────────────────── Generate Interview Questions Models ──────────────────────────

class ExpectedAnswer(BaseModel):
    """Expected answer for different seniority levels."""
    junior_answer: str = Field(..., description="Expected answer for junior level")
    mid_answer: str = Field(..., description="Expected answer for mid level")
    senior_answer: str = Field(..., description="Expected answer for senior level")


class InterviewQuestion(BaseModel):
    """Interview question with expected answers."""
    question_number: int = Field(..., description="Question number for ordering")
    question_text: str = Field(..., description="The interview question")
    category: SkillCategory = Field(..., description="Question category")
    expected_answers: ExpectedAnswer = Field(..., description="Expected answers by seniority level")
    difficulty_level: SeniorityLevel = Field(default=SeniorityLevel.MID, description="Question difficulty level")
    evaluation_criteria: List[str] = Field(default=[], description="Criteria for evaluating answers")


class GenerateQuestionsRequest(BaseModel):
    """Request model for generating interview questions."""
    position_id: str = Field(..., description="Position ID to generate questions for")
    num_questions: int = Field(default=8, description="Number of questions to generate")
    categories: List[SkillCategory] = Field(default=[], description="Categories to include (empty = all)")
    target_seniority: SeniorityLevel = Field(default=SeniorityLevel.MID, description="Target seniority level")
    include_soft_skills: bool = Field(default=True, description="Include soft skills questions")
    custom_requirements: Optional[str] = Field(None, description="Custom requirements for questions")


class GenerateQuestionsResponse(BaseModel):
    """Response model for generated interview questions."""
    success: bool = Field(..., description="Whether the operation was successful")
    questions: List[InterviewQuestion] = Field(default=[], description="Generated interview questions")
    position_info: Optional[Dict[str, Any]] = Field(None, description="Position information used")
    generation_metadata: Dict[str, Any] = Field(default={}, description="Generation metadata")
    error_message: Optional[str] = Field(None, description="Error message if operation failed")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")


# ─────────────────────────── Evaluate Interview Models ──────────────────────────

class QuestionEvaluation(BaseModel):
    """Evaluation of a single interview question."""
    question_number: int = Field(..., description="Question number")
    question_text: str = Field(..., description="The question that was asked")
    candidate_answer: str = Field(..., description="Candidate's actual answer")
    detected_seniority: SeniorityLevel = Field(..., description="Detected seniority level from answer")
    confidence_score: float = Field(..., description="Confidence in the evaluation (0.0 to 1.0)")
    is_valid_response: bool = Field(..., description="Whether the response is valid")
    similarity_scores: Dict[str, float] = Field(default={}, description="Similarity to expected answers")
    evaluation_reasoning: str = Field(..., description="Detailed reasoning for the evaluation")


class InterviewEvaluation(BaseModel):
    """Complete interview evaluation result."""
    overall_seniority: SeniorityLevel = Field(..., description="Overall detected seniority level")
    confidence_score: float = Field(..., description="Overall confidence score")
    question_evaluations: List[QuestionEvaluation] = Field(default=[], description="Individual question evaluations")
    total_questions: int = Field(..., description="Total number of questions evaluated")
    valid_responses: int = Field(..., description="Number of valid responses")
    percentage_correct: float = Field(..., description="Percentage of correct/good answers")
    seniority_distribution: Dict[str, int] = Field(default={}, description="Distribution of seniority levels")
    strengths: List[str] = Field(default=[], description="Candidate's strengths")
    weaknesses: List[str] = Field(default=[], description="Areas for improvement")
    recommendation: str = Field(..., description="Hiring recommendation")
    summary: str = Field(..., description="Overall evaluation summary")


class EvaluateInterviewRequest(BaseModel):
    """Request model for evaluating interview transcripts."""
    interview_id: Optional[str] = Field(None, description="Interview ID if evaluating existing interview")
    transcript: Optional[str] = Field(None, description="Interview transcript text")
    questions: Optional[List[str]] = Field(None, description="List of questions asked")
    position_id: Optional[str] = Field(None, description="Position ID for context")
    candidate_id: Optional[str] = Field(None, description="Candidate ID for context")
    use_four_agent_system: bool = Field(default=True, description="Use the four-agent evaluation system")
    include_detailed_analysis: bool = Field(default=True, description="Include detailed analysis")


class EvaluateInterviewResponse(BaseModel):
    """Response model for interview evaluation."""
    success: bool = Field(..., description="Whether the operation was successful")
    evaluation: Optional[InterviewEvaluation] = Field(None, description="Interview evaluation result")
    processing_metadata: Dict[str, Any] = Field(default={}, description="Processing metadata")
    error_message: Optional[str] = Field(None, description="Error message if operation failed")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")


# ─────────────────────────── MCP Tool Response Models ──────────────────────────

class MCPToolResponse(BaseModel):
    """Base MCP tool response model."""
    tool_name: str = Field(..., description="Name of the MCP tool")
    success: bool = Field(..., description="Whether the operation was successful")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Response timestamp")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")


class MCPError(BaseModel):
    """MCP error response model."""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Error timestamp")
