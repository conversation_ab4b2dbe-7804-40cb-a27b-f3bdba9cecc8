"""
MCP (Model Context Protocol) bridge routes for SmartHR integration.

This module provides FastAPI routes that act as bridges between SmartHR
and the external MCP server, forwarding requests and responses for the
three main MCP tools.
"""

import logging
import httpx
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()

# MCP Server configuration
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://localhost:8001")
MCP_SERVER_TIMEOUT = int(os.getenv("MCP_SERVER_TIMEOUT", "30"))


# Pydantic models for bridge endpoints
class JobPosition(BaseModel):
    position_id: str
    title: str
    description: str
    required_skills: list[str] = []
    preferred_skills: list[str] = []
    seniority_level: str = "mid"
    location: Optional[str] = None
    department: Optional[str] = None


class ExtractCandidatesRequest(BaseModel):
    position: JobPosition
    max_candidates: int = 10
    min_similarity_score: float = 0.3
    include_skills_analysis: bool = True
    filter_by_seniority: bool = False


class GenerateQuestionsRequest(BaseModel):
    position_id: str
    num_questions: int = 8
    categories: list[str] = []
    target_seniority: str = "mid"
    include_soft_skills: bool = True
    custom_requirements: Optional[str] = None


class EvaluateInterviewRequest(BaseModel):
    interview_id: Optional[str] = None
    transcript: Optional[str] = None
    questions: Optional[list[str]] = None
    position_id: Optional[str] = None
    candidate_id: Optional[str] = None
    use_four_agent_system: bool = True
    include_detailed_analysis: bool = True


@router.get("/")
async def mcp_bridge_root():
    """MCP bridge root endpoint with information."""
    return {
        "name": "SmartHR MCP Bridge",
        "version": "1.0.0",
        "description": "Bridge endpoints for SmartHR to MCP server communication",
        "mcp_server_url": MCP_SERVER_URL,
        "tools": [
            "extract_top_candidates",
            "generate_interview_questions",
            "evaluate_interview"
        ],
        "status": "running",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


@router.get("/health")
async def mcp_bridge_health_check():
    """MCP bridge health check endpoint - checks both bridge and MCP server."""
    try:
        # Check if MCP server is reachable
        async with httpx.AsyncClient(timeout=5.0) as client:
            mcp_response = await client.get(f"{MCP_SERVER_URL}/health")
            mcp_healthy = mcp_response.status_code == 200
            mcp_data = mcp_response.json() if mcp_healthy else {}
    except Exception as e:
        mcp_healthy = False
        mcp_data = {"error": str(e)}

    return {
        "bridge_status": "healthy",
        "mcp_server_status": "healthy" if mcp_healthy else "unhealthy",
        "mcp_server_url": MCP_SERVER_URL,
        "mcp_server_data": mcp_data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


@router.get("/tools")
async def list_mcp_tools():
    """List available MCP tools by forwarding to the MCP server."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{MCP_SERVER_URL}/tools")

            if response.status_code == 200:
                return response.json()
            else:
                # Return default tools info if MCP server is unavailable
                return {
                    "tools": [
                        {
                            "name": "extract_top_candidates",
                            "description": "Extract and match top candidates for a job position using vectorization",
                            "enabled": True,
                            "method": "POST",
                            "endpoint": "/mcp/extract-candidates"
                        },
                        {
                            "name": "generate_interview_questions",
                            "description": "Generate interview questions with expected responses based on seniority levels",
                            "enabled": True,
                            "method": "POST",
                            "endpoint": "/mcp/generate-questions"
                        },
                        {
                            "name": "evaluate_interview",
                            "description": "Evaluate interview transcripts using the four-agent evaluation system",
                            "enabled": True,
                            "method": "POST",
                            "endpoint": "/mcp/evaluate-interview"
                        }
                    ],
                    "note": "MCP server unavailable - showing default tools"
                }
    except Exception as e:
        logger.error(f"Error getting tools from MCP server: {e}")
        return {
            "error": "Unable to retrieve tools from MCP server",
            "mcp_server_url": MCP_SERVER_URL
        }


@router.post("/extract-candidates")
async def extract_candidates_bridge(request: ExtractCandidatesRequest):
    """
    Bridge endpoint: Extract and match top candidates for a job position.

    This endpoint forwards the request to the external MCP server and
    returns the response to the SmartHR client.
    """
    try:
        logger.info(f"Forwarding extract candidates request for position: {request.position.title}")

        async with httpx.AsyncClient(timeout=MCP_SERVER_TIMEOUT) as client:
            response = await client.post(
                f"{MCP_SERVER_URL}/extract-candidates",
                json=request.model_dump()
            )

            if response.status_code == 200:
                result = response.json()
                logger.info(f"Successfully received {len(result.get('candidates', []))} candidates from MCP server")
                return result
            else:
                logger.error(f"MCP server error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"MCP server error: {response.text}"
                )

    except httpx.TimeoutException:
        logger.error("MCP server timeout")
        raise HTTPException(status_code=504, detail="MCP server timeout")
    except httpx.RequestError as e:
        logger.error(f"MCP server connection error: {e}")
        raise HTTPException(status_code=503, detail="MCP server unavailable")
    except Exception as e:
        logger.error(f"Error in extract candidates bridge: {e}")
        raise HTTPException(status_code=500, detail=f"Bridge error: {str(e)}")


@router.post("/generate-questions")
async def generate_questions_bridge(request: GenerateQuestionsRequest):
    """
    Bridge endpoint: Generate interview questions with expected responses.

    This endpoint forwards the request to the external MCP server and
    returns the response to the SmartHR client.
    """
    try:
        logger.info(f"Forwarding generate questions request for position: {request.position_id}")

        async with httpx.AsyncClient(timeout=MCP_SERVER_TIMEOUT) as client:
            response = await client.post(
                f"{MCP_SERVER_URL}/generate-questions",
                json=request.model_dump()
            )

            if response.status_code == 200:
                result = response.json()
                logger.info(f"Successfully received {len(result.get('questions', []))} questions from MCP server")
                return result
            else:
                logger.error(f"MCP server error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"MCP server error: {response.text}"
                )

    except httpx.TimeoutException:
        logger.error("MCP server timeout")
        raise HTTPException(status_code=504, detail="MCP server timeout")
    except httpx.RequestError as e:
        logger.error(f"MCP server connection error: {e}")
        raise HTTPException(status_code=503, detail="MCP server unavailable")
    except Exception as e:
        logger.error(f"Error in generate questions bridge: {e}")
        raise HTTPException(status_code=500, detail=f"Bridge error: {str(e)}")


@router.post("/evaluate-interview")
async def evaluate_interview_bridge(request: EvaluateInterviewRequest):
    """
    Bridge endpoint: Evaluate interview transcripts using the four-agent system.

    This endpoint forwards the request to the external MCP server and
    returns the response to the SmartHR client.
    """
    try:
        logger.info(f"Forwarding evaluate interview request for interview: {request.interview_id}")

        async with httpx.AsyncClient(timeout=MCP_SERVER_TIMEOUT) as client:
            response = await client.post(
                f"{MCP_SERVER_URL}/evaluate-interview",
                json=request.model_dump()
            )

            if response.status_code == 200:
                result = response.json()
                logger.info("Successfully received interview evaluation from MCP server")
                return result
            else:
                logger.error(f"MCP server error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"MCP server error: {response.text}"
                )

    except httpx.TimeoutException:
        logger.error("MCP server timeout")
        raise HTTPException(status_code=504, detail="MCP server timeout")
    except httpx.RequestError as e:
        logger.error(f"MCP server connection error: {e}")
        raise HTTPException(status_code=503, detail="MCP server unavailable")
    except Exception as e:
        logger.error(f"Error in evaluate interview bridge: {e}")
        raise HTTPException(status_code=500, detail=f"Bridge error: {str(e)}")


@router.post("/sse")
async def mcp_sse_bridge(request: Request):
    """
    Bridge endpoint: MCP Server-Sent Events streaming.

    This endpoint forwards SSE requests to the external MCP server
    and streams the response back to the client.
    """
    try:
        # Parse request body
        body = await request.json()

        # Forward SSE request to MCP server
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                f"{MCP_SERVER_URL}/sse",
                json=body
            ) as response:
                if response.status_code == 200:
                    # Stream the response back to the client
                    return StreamingResponse(
                        response.aiter_raw(),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Headers": "*",
                        }
                    )
                else:
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"MCP server SSE error: {response.text}"
                    )

    except httpx.RequestError as e:
        logger.error(f"MCP server connection error: {e}")
        raise HTTPException(status_code=503, detail="MCP server unavailable")
    except Exception as e:
        logger.error(f"Error in MCP SSE bridge: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_mcp_configuration():
    """
    Get MCP server configuration by forwarding to the MCP server.
    """
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{MCP_SERVER_URL}/config")

            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": "Unable to retrieve configuration from MCP server",
                    "mcp_server_url": MCP_SERVER_URL,
                    "bridge_info": {
                        "mcp_server_url": MCP_SERVER_URL,
                        "timeout": MCP_SERVER_TIMEOUT
                    }
                }
    except Exception as e:
        logger.error(f"Error getting config from MCP server: {e}")
        return {
            "error": "MCP server unavailable",
            "mcp_server_url": MCP_SERVER_URL
        }


@router.get("/stats")
async def get_mcp_statistics():
    """
    Get MCP server statistics by forwarding to the MCP server.
    """
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{MCP_SERVER_URL}/stats")

            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "bridge_status": "operational",
                    "mcp_server_status": "unavailable",
                    "mcp_server_url": MCP_SERVER_URL,
                    "last_updated": datetime.now(timezone.utc).isoformat()
                }
    except Exception as e:
        logger.error(f"Error getting stats from MCP server: {e}")
        return {
            "error": "MCP server unavailable",
            "mcp_server_url": MCP_SERVER_URL
        }
