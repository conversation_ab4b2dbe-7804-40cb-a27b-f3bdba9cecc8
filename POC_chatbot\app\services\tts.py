"""
Text-to-Speech (TTS) service for the FastAPI chatbot application.

This service provides speech synthesis capabilities using ElevenLabs
and other TTS providers for generating audio responses.
"""

import asyncio
import json
import time
import websockets
from typing import Any, Dict, List, Optional, AsyncIterator
import httpx
from elevenlabs import AsyncElevenLabs
from elevenlabs.types import Voice

from app.services.base import BaseTTSService, ServiceHealth, ServiceStatus
from app.utils.audio import get_audio_mime_type, validate_audio_format
from app.config import settings, get_elevenlabs_config
from app.utils.logging import get_logger

logger = get_logger(__name__)


class ElevenLabsTTSService(BaseTTSService):
    """
    ElevenLabs Text-to-Speech service implementation.
    
    Provides high-quality speech synthesis using ElevenLabs API
    with support for both batch and streaming audio generation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("elevenlabs_tts", config)
        
        # Validate required configuration
        required_keys = ["api_key", "voice_id"]
        self._validate_config(required_keys)
        
        # Initialize ElevenLabs client
        self.client: Optional[AsyncElevenLabs] = None
        self.api_key = config["api_key"]
        self.voice_id = config["voice_id"]
        self.model_id = config.get("model_id", "eleven_monolingual_v1")
        
        # Voice settings
        self.voice_settings = {
            "stability": config.get("stability", 0.5),
            "similarity_boost": config.get("similarity_boost", 0.8),
            "style": config.get("style", 0.0),
            "use_speaker_boost": config.get("use_speaker_boost", True)
        }
        
        # Audio settings
        self.output_format = config.get("output_format", "mp3_44100_128")
        self.optimize_streaming_latency = config.get("optimize_streaming_latency", 3)
        
        # WebSocket streaming
        self.websocket_url = "wss://api.elevenlabs.io/v1/text-to-speech/{voice_id}/stream-input"
        self.streaming_websocket: Optional[websockets.WebSocketServerProtocol] = None
    
    async def initialize(self) -> None:
        """Initialize the ElevenLabs TTS service."""
        await super().initialize()
        
        try:
            # Initialize ElevenLabs async client
            self.client = AsyncElevenLabs(api_key=self.api_key)
            
            # Test the connection by fetching voice info
            voice = await self.client.voices.get(self.voice_id)
            
            self.logger.info(
                "ElevenLabs TTS service initialized successfully",
                voice_id=self.voice_id,
                voice_name=voice.name if hasattr(voice, 'name') else 'Unknown',
                model_id=self.model_id
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ElevenLabs TTS service: {e}")
            raise
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the ElevenLabs TTS service."""
        if not self.client:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message="ElevenLabs TTS client not initialized"
            )
        
        try:
            start_time = time.time()
            
            # Test API connectivity by fetching voice info
            voice = await self.client.voices.get(self.voice_id)
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="ElevenLabs TTS service is operational",
                details={
                    "voice_id": self.voice_id,
                    "voice_name": voice.name if hasattr(voice, 'name') else 'Unknown',
                    "model_id": self.model_id,
                    "output_format": self.output_format,
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"ElevenLabs TTS health check failed: {str(e)}"
            )
    
    async def synthesize_speech(
        self, 
        text: str, 
        voice_id: Optional[str] = None,
        **kwargs: Any
    ) -> bytes:
        """
        Synthesize speech from text using ElevenLabs.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use (uses default if not provided)
            **kwargs: Additional synthesis parameters
            
        Returns:
            Audio data as bytes
        """
        if not self.client:
            raise RuntimeError("ElevenLabs TTS client not initialized")
        
        target_voice_id = voice_id or self.voice_id
        
        self.logger.info(
            "Synthesizing speech with ElevenLabs",
            text_length=len(text),
            voice_id=target_voice_id,
            model_id=self.model_id
        )
        
        try:
            # Prepare synthesis parameters
            synthesis_params = {
                "text": text,
                "voice": target_voice_id,
                "model": self.model_id,
                "voice_settings": self.voice_settings.copy()
            }
            
            # Override voice settings with kwargs
            for key in ["stability", "similarity_boost", "style", "use_speaker_boost"]:
                if key in kwargs:
                    synthesis_params["voice_settings"][key] = kwargs[key]
            
            # Call ElevenLabs API
            start_time = time.time()
            audio_generator = await self.client.generate(
                **synthesis_params,
                stream=False
            )
            
            # Collect audio data
            audio_data = b""
            async for chunk in audio_generator:
                audio_data += chunk
            
            processing_time = time.time() - start_time
            
            self.logger.info(
                "Speech synthesis completed",
                audio_size_bytes=len(audio_data),
                processing_time_ms=round(processing_time * 1000, 2),
                text_preview=text[:50] + "..." if len(text) > 50 else text
            )
            
            return audio_data
            
        except Exception as e:
            self.logger.error(
                "Error synthesizing speech",
                error=str(e),
                text_length=len(text),
                voice_id=target_voice_id
            )
            raise
    
    async def stream_speech(
        self, 
        text: str, 
        voice_id: Optional[str] = None,
        **kwargs: Any
    ) -> AsyncIterator[bytes]:
        """
        Stream speech synthesis in real-time.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use (uses default if not provided)
            **kwargs: Additional synthesis parameters
            
        Yields:
            Audio data chunks
        """
        if not self.client:
            raise RuntimeError("ElevenLabs TTS client not initialized")
        
        target_voice_id = voice_id or self.voice_id
        
        self.logger.info(
            "Starting streaming speech synthesis",
            text_length=len(text),
            voice_id=target_voice_id
        )
        
        try:
            # Prepare synthesis parameters
            synthesis_params = {
                "text": text,
                "voice": target_voice_id,
                "model": self.model_id,
                "voice_settings": self.voice_settings.copy(),
                "stream": True
            }
            
            # Override voice settings with kwargs
            for key in ["stability", "similarity_boost", "style", "use_speaker_boost"]:
                if key in kwargs:
                    synthesis_params["voice_settings"][key] = kwargs[key]
            
            # Stream audio from ElevenLabs
            audio_generator = await self.client.generate(**synthesis_params)
            
            chunk_count = 0
            total_size = 0
            
            async for chunk in audio_generator:
                chunk_count += 1
                total_size += len(chunk)
                
                self.logger.debug(
                    f"Streaming audio chunk {chunk_count}",
                    chunk_size=len(chunk),
                    total_size=total_size
                )
                
                yield chunk
            
            self.logger.info(
                "Streaming speech synthesis completed",
                total_chunks=chunk_count,
                total_size_bytes=total_size
            )
            
        except Exception as e:
            self.logger.error(
                "Error in streaming speech synthesis",
                error=str(e),
                text_length=len(text),
                voice_id=target_voice_id
            )
            raise
    
    async def start_websocket_stream(
        self, 
        voice_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        Start a WebSocket streaming session with ElevenLabs.
        
        This enables real-time text-to-speech streaming where text
        can be sent incrementally and audio is received in real-time.
        
        Args:
            voice_id: Voice ID to use
            **kwargs: Additional streaming parameters
        """
        target_voice_id = voice_id or self.voice_id
        websocket_url = self.websocket_url.format(voice_id=target_voice_id)
        
        self.logger.info(
            "Starting ElevenLabs WebSocket stream",
            voice_id=target_voice_id,
            url=websocket_url
        )
        
        try:
            # Connect to ElevenLabs WebSocket
            headers = {
                "xi-api-key": self.api_key
            }
            
            self.streaming_websocket = await websockets.connect(
                websocket_url,
                extra_headers=headers
            )
            
            # Send initial configuration
            config_message = {
                "text": " ",  # Initial space to start the stream
                "voice_settings": self.voice_settings,
                "generation_config": {
                    "chunk_length_schedule": [120, 160, 250, 290]
                }
            }
            
            await self.streaming_websocket.send(json.dumps(config_message))
            
            self.logger.info("ElevenLabs WebSocket stream started successfully")
            
        except Exception as e:
            self.logger.error(f"Error starting WebSocket stream: {e}")
            raise
    
    async def send_text_to_stream(self, text: str) -> None:
        """
        Send text to the active WebSocket stream.
        
        Args:
            text: Text to convert to speech
        """
        if not self.streaming_websocket:
            raise RuntimeError("WebSocket stream not started")
        
        try:
            message = {"text": text}
            await self.streaming_websocket.send(json.dumps(message))
            
            self.logger.debug(
                "Text sent to WebSocket stream",
                text_length=len(text),
                preview=text[:50] + "..." if len(text) > 50 else text
            )
            
        except Exception as e:
            self.logger.error(f"Error sending text to stream: {e}")
            raise
    
    async def receive_audio_from_stream(self) -> AsyncIterator[bytes]:
        """
        Receive audio data from the active WebSocket stream.
        
        Yields:
            Audio data chunks from the stream
        """
        if not self.streaming_websocket:
            raise RuntimeError("WebSocket stream not started")
        
        try:
            async for message in self.streaming_websocket:
                try:
                    data = json.loads(message)
                    
                    if "audio" in data:
                        # Decode base64 audio data
                        import base64
                        audio_data = base64.b64decode(data["audio"])
                        yield audio_data
                    
                    elif "isFinal" in data and data["isFinal"]:
                        self.logger.info("WebSocket stream completed")
                        break
                        
                except json.JSONDecodeError:
                    # Assume raw audio data
                    yield message.encode() if isinstance(message, str) else message
                    
        except Exception as e:
            self.logger.error(f"Error receiving audio from stream: {e}")
            raise
    
    async def close_websocket_stream(self) -> None:
        """Close the active WebSocket stream."""
        if self.streaming_websocket:
            try:
                # Send end-of-stream message
                end_message = {"text": ""}
                await self.streaming_websocket.send(json.dumps(end_message))
                
                # Close connection
                await self.streaming_websocket.close()
                self.streaming_websocket = None
                
                self.logger.info("ElevenLabs WebSocket stream closed")
                
            except Exception as e:
                self.logger.error(f"Error closing WebSocket stream: {e}")
    
    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """
        Get list of available voices from ElevenLabs.
        
        Returns:
            List of voice information dictionaries
        """
        if not self.client:
            raise RuntimeError("ElevenLabs TTS client not initialized")
        
        try:
            voices = await self.client.voices.get_all()
            
            voice_list = []
            for voice in voices.voices:
                voice_info = {
                    "voice_id": voice.voice_id,
                    "name": voice.name,
                    "category": voice.category,
                    "description": getattr(voice, 'description', ''),
                    "preview_url": getattr(voice, 'preview_url', ''),
                }
                voice_list.append(voice_info)
            
            self.logger.info(f"Retrieved {len(voice_list)} available voices")
            return voice_list
            
        except Exception as e:
            self.logger.error(f"Error getting available voices: {e}")
            raise


# Factory function to create TTS service based on configuration
def create_tts_service(provider: str = "elevenlabs") -> BaseTTSService:
    """
    Create a TTS service instance based on the provider.
    
    Args:
        provider: TTS provider name ("elevenlabs", etc.)
        
    Returns:
        TTS service instance
    """
    if provider.lower() == "elevenlabs":
        config = get_elevenlabs_config()
        return ElevenLabsTTSService(config)
    else:
        raise ValueError(f"Unsupported TTS provider: {provider}")


# Global TTS service instance
tts_service = create_tts_service("elevenlabs")
