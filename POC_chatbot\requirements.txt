# FastAPI Chatbot Requirements
# Core framework and server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Twilio integration
twilio==8.10.3

# ElevenLabs TTS
elevenlabs==0.2.26
httpx==0.25.2

# Groq for LLM
groq==0.4.1

# WebSocket support
websockets==12.0

# Configuration and environment
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for external APIs
httpx==0.25.2
aiohttp==3.9.1

# Frontend
streamlit==1.28.1

# Audio processing
pydub==0.25.1

# Async file operations
aiofiles==23.2.1

# Logging and monitoring
structlog==23.2.0

# Data validation and serialization
typing-extensions==4.8.0

# Resilience and retry logic
tenacity==8.2.3

# Base64 encoding/decoding for audio
base64io==1.0.3

# JSON handling
orjson==3.9.10

# Date/time utilities
python-dateutil==2.8.2

# Retry logic for external APIs
tenacity==8.2.3

# Development and testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2  # For testing HTTP clients
pytest-cov==4.1.0

# Code quality
black==23.11.0
flake8==6.1.0
isort==5.12.0

# Type checking
mypy==1.7.1

# Security
cryptography>=42.0.0

# CORS support (built into FastAPI)
# python-cors not needed - FastAPI has built-in CORS middleware

# Session management (optional - for Redis support)
redis==5.0.1
aioredis==2.0.1

# Database support (optional - for PostgreSQL)
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23

# Environment-specific dependencies
# Uncomment based on deployment needs:
# gunicorn==21.2.0  # For production WSGI server
# docker==6.1.3     # For Docker integration
# boto3==1.34.0     # For AWS services
