"""
SmartHR API client for external MCP server.

This module provides HTTP client functionality to communicate with
the SmartHR system from the external MCP server.
"""

import httpx
import logging
from typing import List, Dict, Any, Optional
from config import get_mcp_config, get_smarthr_api_config
from models import JobPosition, SeniorityLevel

logger = logging.getLogger(__name__)


class SmartHRClient:
    """HTTP client for SmartHR API communication."""
    
    def __init__(self):
        self.config = get_smarthr_api_config()
        self.base_url = self.config["base_url"]
        self.timeout = self.config["timeout"]
        self.api_key = self.config.get("api_key")
        
        # Setup headers
        self.headers = {"Content-Type": "application/json"}
        if self.api_key:
            self.headers["Authorization"] = f"Bearer {self.api_key}"
    
    async def get_candidates(self, position: JobPosition, max_candidates: int = 10) -> List[Dict[str, Any]]:
        """Get candidates from SmartHR database."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # Call SmartHR candidate search endpoint
                response = await client.post(
                    f"{self.base_url}/candidates/search",
                    json={
                        "position_id": position.position_id,
                        "title": position.title,
                        "description": position.description,
                        "required_skills": position.required_skills,
                        "preferred_skills": position.preferred_skills,
                        "seniority_level": position.seniority_level.value,
                        "max_results": max_candidates
                    },
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("candidates", [])
                else:
                    logger.error(f"SmartHR API error: {response.status_code} - {response.text}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error calling SmartHR candidates API: {e}")
            return []
    
    async def get_position_info(self, position_id: str) -> Optional[Dict[str, Any]]:
        """Get position information from SmartHR."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/positions/{position_id}",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"SmartHR API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling SmartHR positions API: {e}")
            return None
    
    async def generate_questions(self, position_id: str, num_questions: int = 8, 
                               target_seniority: SeniorityLevel = SeniorityLevel.MID) -> List[Dict[str, Any]]:
        """Generate interview questions via SmartHR."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/interviews/generate-questions",
                    json={
                        "position_id": position_id,
                        "num_questions": num_questions,
                        "target_seniority": target_seniority.value
                    },
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("questions", [])
                else:
                    logger.error(f"SmartHR API error: {response.status_code} - {response.text}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error calling SmartHR questions API: {e}")
            return []
    
    async def evaluate_interview(self, interview_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Evaluate interview via SmartHR four-agent system."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/interviews/evaluate",
                    json=interview_data,
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"SmartHR API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling SmartHR evaluation API: {e}")
            return None
    
    async def get_embeddings(self, text: str) -> Optional[List[float]]:
        """Get embeddings from SmartHR embedding service."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/embeddings/generate",
                    json={"text": text},
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("embedding", [])
                else:
                    logger.error(f"SmartHR API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling SmartHR embeddings API: {e}")
            return None
    
    async def vector_search(self, query_vector: List[float], top_k: int = 10) -> List[Dict[str, Any]]:
        """Perform vector similarity search via SmartHR."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/search/vector",
                    json={
                        "query_vector": query_vector,
                        "top_k": top_k
                    },
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("results", [])
                else:
                    logger.error(f"SmartHR API error: {response.status_code} - {response.text}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error calling SmartHR vector search API: {e}")
            return []
    
    async def health_check(self) -> bool:
        """Check if SmartHR API is healthy."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(
                    f"{self.base_url}/health",
                    headers=self.headers
                )
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"SmartHR health check failed: {e}")
            return False


# Global client instance
smarthr_client = SmartHRClient()


def get_smarthr_client() -> SmartHRClient:
    """Get SmartHR client instance."""
    return smarthr_client
