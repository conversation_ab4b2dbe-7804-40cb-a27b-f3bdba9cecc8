"""
Resilience utilities for the FastAPI chatbot application.

This module provides retry logic, circuit breaker patterns, and other
resilience mechanisms for handling external service failures gracefully.
"""

import asyncio
import time
from typing import Any, Callable, Dict, Optional, Type, Union, List
from functools import wraps
from enum import Enum
import random

from app.utils.logging import get_logger

logger = get_logger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, rejecting requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class RetryStrategy(Enum):
    """Retry strategy types."""
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    JITTER = "jitter"


class CircuitBreakerError(Exception):
    """Exception raised when circuit breaker is open."""
    pass


class MaxRetriesExceededError(Exception):
    """Exception raised when maximum retries are exceeded."""
    pass


class CircuitBreaker:
    """
    Circuit breaker implementation for service resilience.
    
    Prevents cascading failures by temporarily stopping calls to
    a failing service and allowing it time to recover.
    """
    
    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: Type[Exception] = Exception
    ):
        """
        Initialize circuit breaker.
        
        Args:
            name: Circuit breaker name for logging
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying again
            expected_exception: Exception type that triggers circuit breaker
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        # State tracking
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.success_count = 0
        
        logger.info(
            f"Circuit breaker '{name}' initialized",
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout
        )
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerError: When circuit is open
        """
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info(f"Circuit breaker '{self.name}' moved to HALF_OPEN")
            else:
                raise CircuitBreakerError(
                    f"Circuit breaker '{self.name}' is OPEN"
                )
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            self._on_success()
            return result
            
        except self.expected_exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _on_success(self) -> None:
        """Handle successful call."""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.CLOSED
            self.failure_count = 0
            logger.info(f"Circuit breaker '{self.name}' CLOSED after successful call")
        
        self.success_count += 1
    
    def _on_failure(self) -> None:
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(
                f"Circuit breaker '{self.name}' OPENED",
                failure_count=self.failure_count,
                threshold=self.failure_threshold
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time,
            "failure_threshold": self.failure_threshold,
            "recovery_timeout": self.recovery_timeout,
        }


def retry_with_backoff(
    max_retries: int = 3,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: Union[Type[Exception], tuple] = Exception,
    jitter: bool = True
):
    """
    Decorator for retrying functions with configurable backoff strategies.
    
    Args:
        max_retries: Maximum number of retry attempts
        strategy: Retry strategy (fixed, exponential, linear, jitter)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        exceptions: Exception types to retry on
        jitter: Whether to add random jitter to delays
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(
                            f"Max retries ({max_retries}) exceeded for {func.__name__}",
                            error=str(e),
                            attempt=attempt + 1
                        )
                        raise MaxRetriesExceededError(
                            f"Max retries exceeded for {func.__name__}: {str(e)}"
                        ) from e
                    
                    delay = _calculate_delay(attempt, strategy, base_delay, max_delay, jitter)
                    
                    logger.warning(
                        f"Retry attempt {attempt + 1}/{max_retries} for {func.__name__}",
                        error=str(e),
                        delay=delay
                    )
                    
                    await asyncio.sleep(delay)
            
            # This should never be reached, but just in case
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(
                            f"Max retries ({max_retries}) exceeded for {func.__name__}",
                            error=str(e),
                            attempt=attempt + 1
                        )
                        raise MaxRetriesExceededError(
                            f"Max retries exceeded for {func.__name__}: {str(e)}"
                        ) from e
                    
                    delay = _calculate_delay(attempt, strategy, base_delay, max_delay, jitter)
                    
                    logger.warning(
                        f"Retry attempt {attempt + 1}/{max_retries} for {func.__name__}",
                        error=str(e),
                        delay=delay
                    )
                    
                    time.sleep(delay)
            
            # This should never be reached, but just in case
            raise last_exception
        
        # Return appropriate wrapper based on function type
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def _calculate_delay(
    attempt: int,
    strategy: RetryStrategy,
    base_delay: float,
    max_delay: float,
    jitter: bool
) -> float:
    """Calculate delay for retry attempt based on strategy."""
    if strategy == RetryStrategy.FIXED:
        delay = base_delay
    elif strategy == RetryStrategy.EXPONENTIAL:
        delay = base_delay * (2 ** attempt)
    elif strategy == RetryStrategy.LINEAR:
        delay = base_delay * (attempt + 1)
    elif strategy == RetryStrategy.JITTER:
        delay = base_delay + random.uniform(0, base_delay)
    else:
        delay = base_delay
    
    # Apply jitter if enabled
    if jitter and strategy != RetryStrategy.JITTER:
        jitter_amount = delay * 0.1  # 10% jitter
        delay += random.uniform(-jitter_amount, jitter_amount)
    
    # Ensure delay doesn't exceed maximum
    return min(delay, max_delay)


class ServiceRegistry:
    """
    Registry for managing circuit breakers across services.
    """
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
    
    def get_circuit_breaker(
        self,
        service_name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: Type[Exception] = Exception
    ) -> CircuitBreaker:
        """
        Get or create a circuit breaker for a service.
        
        Args:
            service_name: Name of the service
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying again
            expected_exception: Exception type that triggers circuit breaker
            
        Returns:
            Circuit breaker instance
        """
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(
                name=service_name,
                failure_threshold=failure_threshold,
                recovery_timeout=recovery_timeout,
                expected_exception=expected_exception
            )
        
        return self.circuit_breakers[service_name]
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all circuit breakers."""
        return {
            name: breaker.get_stats()
            for name, breaker in self.circuit_breakers.items()
        }


# Global service registry instance
service_registry = ServiceRegistry()


# Convenience functions for common retry patterns
def retry_api_call(max_retries: int = 3, base_delay: float = 1.0):
    """Retry decorator optimized for API calls."""
    return retry_with_backoff(
        max_retries=max_retries,
        strategy=RetryStrategy.EXPONENTIAL,
        base_delay=base_delay,
        max_delay=30.0,
        exceptions=(ConnectionError, TimeoutError, Exception),
        jitter=True
    )


def retry_database_operation(max_retries: int = 2, base_delay: float = 0.5):
    """Retry decorator optimized for database operations."""
    return retry_with_backoff(
        max_retries=max_retries,
        strategy=RetryStrategy.LINEAR,
        base_delay=base_delay,
        max_delay=5.0,
        exceptions=(ConnectionError, TimeoutError),
        jitter=False
    )


async def with_circuit_breaker(
    service_name: str,
    func: Callable,
    *args,
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    **kwargs
) -> Any:
    """
    Execute function with circuit breaker protection.
    
    Args:
        service_name: Name of the service for circuit breaker
        func: Function to execute
        *args: Function arguments
        failure_threshold: Circuit breaker failure threshold
        recovery_timeout: Circuit breaker recovery timeout
        **kwargs: Function keyword arguments
        
    Returns:
        Function result
    """
    circuit_breaker = service_registry.get_circuit_breaker(
        service_name=service_name,
        failure_threshold=failure_threshold,
        recovery_timeout=recovery_timeout
    )
    
    return await circuit_breaker.call(func, *args, **kwargs)
