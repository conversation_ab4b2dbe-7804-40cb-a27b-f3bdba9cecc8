"""
Test cases for webhook endpoints.

This module contains comprehensive tests for SMS and Voice webhook
endpoints including Twilio signature validation and response generation.
"""

import pytest
import hmac
import hashlib
import base64
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from urllib.parse import urlencode

from app.main import app
from app.services.chatbot import ChatbotService
from app.services.twilio_service import TwilioService


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_chatbot_service():
    """Mock chatbot service."""
    service = AsyncMock(spec=ChatbotService)
    service.process_message.return_value = "Hello! This is a test response."
    return service


@pytest.fixture
def mock_twilio_service():
    """Mock Twilio service."""
    service = AsyncMock(spec=TwilioService)
    service.generate_sms_twiml_response.return_value = '<?xml version="1.0" encoding="UTF-8"?><Response><Message>Hello! This is a test response.</Message></Response>'
    service.generate_twiml_response.return_value = '<?xml version="1.0" encoding="UTF-8"?><Response><Say>Hello! Welcome to our AI assistant.</Say></Response>'
    return service


def generate_twilio_signature(url: str, params: dict, auth_token: str) -> str:
    """Generate Twilio signature for testing."""
    signature_string = url
    sorted_params = sorted(params.items())
    for key, value in sorted_params:
        signature_string += f"{key}{value}"
    
    signature = base64.b64encode(
        hmac.new(
            auth_token.encode('utf-8'),
            signature_string.encode('utf-8'),
            hashlib.sha1
        ).digest()
    ).decode('utf-8')
    
    return signature


class TestSMSWebhook:
    """Test cases for SMS webhook endpoint."""
    
    def test_sms_webhook_success(self, client, mock_chatbot_service, mock_twilio_service):
        """Test successful SMS webhook processing."""
        # Prepare test data
        webhook_data = {
            "MessageSid": "SM**********abcdef**********abcdef",
            "AccountSid": "AC**********abcdef**********abcdef",
            "From": "+**********",
            "To": "+**********",
            "Body": "Hello, AI assistant!",
            "NumMedia": "0",
            "MessageStatus": "received",
            "ApiVersion": "2010-04-01"
        }
        
        # Generate signature
        url = "http://testserver/sms-webhook"
        auth_token = "test_auth_token"
        signature = generate_twilio_signature(url, webhook_data, auth_token)
        
        headers = {
            "X-Twilio-Signature": signature,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # Mock dependencies
        with patch("app.api.webhooks.get_chatbot_service", return_value=mock_chatbot_service), \
             patch("app.api.webhooks.get_twilio_service", return_value=mock_twilio_service), \
             patch("app.config.settings.twilio_auth_token", auth_token):
            
            response = client.post("/sms-webhook", data=webhook_data, headers=headers)
        
        # Assertions
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/xml; charset=utf-8"
        assert "Hello! This is a test response." in response.text
        
        # Verify service calls
        mock_chatbot_service.process_message.assert_called_once()
        mock_twilio_service.generate_sms_twiml_response.assert_called_once()
    
    def test_sms_webhook_invalid_signature(self, client):
        """Test SMS webhook with invalid signature."""
        webhook_data = {
            "MessageSid": "SM**********abcdef**********abcdef",
            "From": "+**********",
            "To": "+**********",
            "Body": "Hello, AI assistant!",
        }
        
        headers = {
            "X-Twilio-Signature": "invalid_signature",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        response = client.post("/sms-webhook", data=webhook_data, headers=headers)
        
        # Should return 403 for invalid signature
        assert response.status_code == 403
    
    def test_sms_webhook_missing_required_fields(self, client):
        """Test SMS webhook with missing required fields."""
        webhook_data = {
            "MessageSid": "SM**********abcdef**********abcdef",
            # Missing From, To, Body
        }
        
        response = client.post("/sms-webhook", data=webhook_data)
        
        # Should return 422 for validation error
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_sms_webhook_chatbot_error(self, client, mock_chatbot_service, mock_twilio_service):
        """Test SMS webhook when chatbot service fails."""
        # Configure mock to raise exception
        mock_chatbot_service.process_message.side_effect = Exception("Chatbot service error")
        
        webhook_data = {
            "MessageSid": "SM**********abcdef**********abcdef",
            "AccountSid": "AC**********abcdef**********abcdef",
            "From": "+**********",
            "To": "+**********",
            "Body": "Hello, AI assistant!",
            "NumMedia": "0",
        }
        
        url = "http://testserver/sms-webhook"
        auth_token = "test_auth_token"
        signature = generate_twilio_signature(url, webhook_data, auth_token)
        
        headers = {
            "X-Twilio-Signature": signature,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        with patch("app.api.webhooks.get_chatbot_service", return_value=mock_chatbot_service), \
             patch("app.api.webhooks.get_twilio_service", return_value=mock_twilio_service), \
             patch("app.config.settings.twilio_auth_token", auth_token):
            
            response = client.post("/sms-webhook", data=webhook_data, headers=headers)
        
        # Should still return 200 with error message
        assert response.status_code == 200
        assert "error" in response.text.lower() or "sorry" in response.text.lower()


class TestVoiceWebhook:
    """Test cases for Voice webhook endpoint."""
    
    def test_voice_webhook_success(self, client, mock_twilio_service):
        """Test successful voice webhook processing."""
        webhook_data = {
            "CallSid": "CA**********abcdef**********abcdef",
            "AccountSid": "AC**********abcdef**********abcdef",
            "From": "+**********",
            "To": "+**********",
            "CallStatus": "in-progress",
            "Direction": "inbound",
            "ApiVersion": "2010-04-01"
        }
        
        url = "http://testserver/voice-webhook"
        auth_token = "test_auth_token"
        signature = generate_twilio_signature(url, webhook_data, auth_token)
        
        headers = {
            "X-Twilio-Signature": signature,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        with patch("app.api.webhooks.get_twilio_service", return_value=mock_twilio_service), \
             patch("app.config.settings.twilio_auth_token", auth_token):
            
            response = client.post("/voice-webhook", data=webhook_data, headers=headers)
        
        # Assertions
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/xml; charset=utf-8"
        assert "Say" in response.text or "Connect" in response.text
        
        # Verify service calls
        mock_twilio_service.generate_twiml_response.assert_called_once()
    
    def test_voice_webhook_with_streaming(self, client, mock_twilio_service):
        """Test voice webhook with streaming enabled."""
        webhook_data = {
            "CallSid": "CA**********abcdef**********abcdef",
            "From": "+**********",
            "To": "+**********",
            "CallStatus": "in-progress",
        }
        
        # Mock TwiML response with streaming
        mock_twilio_service.generate_twiml_response.return_value = '''<?xml version="1.0" encoding="UTF-8"?>
        <Response>
            <Connect>
                <Stream url="wss://example.com/media-stream" />
            </Connect>
        </Response>'''
        
        url = "http://testserver/voice-webhook"
        auth_token = "test_auth_token"
        signature = generate_twilio_signature(url, webhook_data, auth_token)
        
        headers = {
            "X-Twilio-Signature": signature,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        with patch("app.api.webhooks.get_twilio_service", return_value=mock_twilio_service), \
             patch("app.config.settings.twilio_auth_token", auth_token), \
             patch("app.config.settings.enable_voice_streaming", True):
            
            response = client.post("/voice-webhook", data=webhook_data, headers=headers)
        
        assert response.status_code == 200
        assert "Connect" in response.text
        assert "Stream" in response.text
    
    def test_voice_webhook_call_completed(self, client, mock_twilio_service):
        """Test voice webhook for completed call."""
        webhook_data = {
            "CallSid": "CA**********abcdef**********abcdef",
            "From": "+**********",
            "To": "+**********",
            "CallStatus": "completed",
            "CallDuration": "120",
        }
        
        url = "http://testserver/voice-webhook"
        auth_token = "test_auth_token"
        signature = generate_twilio_signature(url, webhook_data, auth_token)
        
        headers = {
            "X-Twilio-Signature": signature,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        with patch("app.api.webhooks.get_twilio_service", return_value=mock_twilio_service), \
             patch("app.config.settings.twilio_auth_token", auth_token):
            
            response = client.post("/voice-webhook", data=webhook_data, headers=headers)
        
        assert response.status_code == 200


class TestWebhookValidation:
    """Test cases for webhook validation."""
    
    def test_valid_phone_number_formats(self, client):
        """Test various valid phone number formats."""
        valid_numbers = [
            "+**********",
            "+**********1",
            "+44**********",
            "+33123456789"
        ]
        
        for number in valid_numbers:
            webhook_data = {
                "MessageSid": "SM**********abcdef**********abcdef",
                "From": number,
                "To": "+**********",
                "Body": "Test message",
            }
            
            # Note: This test would need proper signature validation
            # For now, we're testing the validation logic
            assert number.startswith("+")
            assert len(number) >= 10
    
    def test_message_content_sanitization(self):
        """Test message content sanitization."""
        from app.utils.validation import sanitize_message_content
        
        # Test cases
        test_cases = [
            ("Hello world!", "Hello world!"),
            ("Hello\nworld!", "Hello world!"),
            ("Hello\tworld!", "Hello world!"),
            ("A" * 1000, "A" * 500),  # Assuming 500 char limit
            ("", ""),
            ("   spaces   ", "spaces"),
        ]
        
        for input_text, expected in test_cases:
            result = sanitize_message_content(input_text, max_length=500)
            if expected:
                assert result == expected
            else:
                assert len(result) <= 500


@pytest.mark.integration
class TestWebhookIntegration:
    """Integration tests for webhook endpoints."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_sms_flow(self, client):
        """Test complete SMS flow from webhook to response."""
        # This would be a full integration test
        # requiring actual service instances
        pass
    
    @pytest.mark.asyncio
    async def test_end_to_end_voice_flow(self, client):
        """Test complete voice flow from webhook to TwiML."""
        # This would be a full integration test
        # requiring actual service instances
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
