# SmartHR MCP Server

External Model Context Protocol (MCP) server for SmartHR integration.

## Overview

This is a standalone MCP server that provides three main tools for SmartHR integration:

1. **extract_top_candidates** - Vectorize and match candidates against job positions
2. **generate_interview_questions** - Create interview questions with expected responses
3. **evaluate_interview** - Evaluate interview transcripts using four-agent system

The server communicates with SmartHR via HTTP API and serves MCP clients through Server-Sent Events (SSE).

## Architecture

```
MCP Client → External MCP Server → SmartHR API
```

- **External MCP Server**: Standalone FastAPI application (this project)
- **SmartHR API**: Main SmartHR system with bridge endpoints
- **MCP Client**: Any application that consumes MCP tools

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Create a `.env` file:

```env
# Server configuration
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8001
MCP_SERVER_DEBUG=false

# SmartHR API configuration
SMARTHR_API_URL=http://localhost:8080
SMARTHR_API_TIMEOUT=30

# Tool configuration
ENABLE_EXTRACT_CANDIDATES=true
ENABLE_GENERATE_QUESTIONS=true
ENABLE_EVALUATE_INTERVIEW=true
```

### 3. Run the Server

```bash
# Start the server
python main.py start

# Or run health check first
python main.py health

# Show configuration
python main.py config
```

### 4. Test the Server

```bash
# Health check
curl http://localhost:8001/health

# List tools
curl http://localhost:8001/tools

# Test extract candidates
curl -X POST http://localhost:8001/extract-candidates \
  -H "Content-Type: application/json" \
  -d '{
    "position": {
      "position_id": "pos_123",
      "title": "Senior Python Developer",
      "description": "Looking for experienced Python developer",
      "required_skills": ["Python", "FastAPI", "PostgreSQL"],
      "seniority_level": "senior"
    },
    "max_candidates": 5
  }'
```

## Docker Deployment

### Build and Run

```bash
# Build image
docker build -t smarthr-mcp-server .

# Run container
docker run -p 8001:8001 --env-file .env smarthr-mcp-server

# Or use docker-compose
docker-compose up -d
```

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MCP_SERVER_HOST` | `0.0.0.0` | Server host |
| `MCP_SERVER_PORT` | `8001` | Server port |
| `SMARTHR_API_URL` | `http://localhost:8080` | SmartHR API URL |
| `SMARTHR_API_TIMEOUT` | `30` | API timeout in seconds |
| `ENABLE_EXTRACT_CANDIDATES` | `true` | Enable extract candidates tool |
| `ENABLE_GENERATE_QUESTIONS` | `true` | Enable generate questions tool |
| `ENABLE_EVALUATE_INTERVIEW` | `true` | Enable evaluate interview tool |

## API Endpoints

### Core Endpoints

- `GET /` - Server information
- `GET /health` - Health check
- `GET /tools` - List available tools
- `GET /config` - Server configuration
- `GET /stats` - Server statistics

### Tool Endpoints

- `POST /extract-candidates` - Extract top candidates
- `POST /generate-questions` - Generate interview questions
- `POST /evaluate-interview` - Evaluate interview transcripts

### MCP Protocol

- `POST /sse` - Server-Sent Events endpoint for MCP clients

## Tool Usage

### Extract Top Candidates

```python
import httpx

async def extract_candidates():
    async with httpx.AsyncClient() as client:
        response = await client.post("http://localhost:8001/extract-candidates", json={
            "position": {
                "position_id": "pos_123",
                "title": "Senior Python Developer",
                "description": "Looking for experienced Python developer",
                "required_skills": ["Python", "FastAPI", "PostgreSQL"],
                "seniority_level": "senior"
            },
            "max_candidates": 10,
            "min_similarity_score": 0.3
        })
        return response.json()
```

### Generate Interview Questions

```python
async def generate_questions():
    async with httpx.AsyncClient() as client:
        response = await client.post("http://localhost:8001/generate-questions", json={
            "position_id": "pos_123",
            "num_questions": 8,
            "target_seniority": "senior",
            "include_soft_skills": True
        })
        return response.json()
```

### Evaluate Interview

```python
async def evaluate_interview():
    async with httpx.AsyncClient() as client:
        response = await client.post("http://localhost:8001/evaluate-interview", json={
            "interview_id": "int_456",
            "transcript": "Q: Tell me about Python... A: Python is...",
            "position_id": "pos_123",
            "use_four_agent_system": True
        })
        return response.json()
```

## MCP Client Integration

### Server-Sent Events

```javascript
const eventSource = new EventSource('http://localhost:8001/sse', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    tool: 'extract_top_candidates',
    arguments: {
      position: {
        position_id: 'pos_123',
        title: 'Senior Python Developer',
        // ... other fields
      }
    },
    request_id: 'req_123'
  })
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Event:', data.event, 'Data:', data);
};
```

## Development

### Project Structure

```
mcp_server/
├── __init__.py          # Module initialization
├── main.py              # Entry point and CLI
├── server.py            # FastAPI server implementation
├── config.py            # Configuration management
├── models.py            # Pydantic models
├── tools.py             # MCP tools implementation
├── smarthr_client.py    # SmartHR API client
├── requirements.txt     # Python dependencies
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker Compose configuration
└── README.md           # This file
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock

# Run tests
pytest
```

### Logging

Logs are written to both console and `mcp_server.log` file. Configure log level with `LOG_LEVEL` environment variable.

## Troubleshooting

### Common Issues

1. **SmartHR API Connection Failed**
   - Check `SMARTHR_API_URL` configuration
   - Ensure SmartHR server is running
   - Verify network connectivity

2. **Tool Disabled Errors**
   - Check tool enable flags in configuration
   - Verify SmartHR API endpoints are available

3. **Port Already in Use**
   - Change `MCP_SERVER_PORT` to an available port
   - Check for other services using port 8001

### Health Check

```bash
# Check server health
python main.py health

# Check specific endpoints
curl http://localhost:8001/health
curl http://localhost:8001/tools
```

## License

This project is part of the SmartHR system.
