@echo off
REM SmartHR + MCP Server Startup Script for Windows

setlocal enabledelayedexpansion

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)
echo [SUCCESS] Docker is running

REM Check for docker-compose
docker-compose --version >nul 2>&1
if not errorlevel 1 (
    set DOCKER_COMPOSE_CMD=docker-compose
) else (
    docker compose version >nul 2>&1
    if not errorlevel 1 (
        set DOCKER_COMPOSE_CMD=docker compose
    ) else (
        echo [ERROR] docker-compose or 'docker compose' not found. Please install Docker Compose.
        exit /b 1
    )
)
echo [SUCCESS] Docker Compose is available: !DOCKER_COMPOSE_CMD!

REM Setup environment file
if not exist .env (
    if exist .env.example (
        echo [WARNING] .env file not found. Creating from .env.example...
        copy .env.example .env >nul
        echo [WARNING] Please edit .env file with your configuration before running again.
        echo [WARNING] At minimum, you need to configure OpenAI/Azure OpenAI settings.
        pause
        exit /b 1
    ) else (
        echo [ERROR] .env.example file not found. Cannot create .env file.
        exit /b 1
    )
) else (
    echo [SUCCESS] .env file found
)

REM Handle command line arguments
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=start

if "%COMMAND%"=="start" goto start
if "%COMMAND%"=="test" goto test
if "%COMMAND%"=="logs" goto logs
if "%COMMAND%"=="stop" goto stop
if "%COMMAND%"=="restart" goto restart
if "%COMMAND%"=="clean" goto clean
if "%COMMAND%"=="health" goto health
if "%COMMAND%"=="urls" goto urls
goto usage

:start
echo [INFO] Starting SmartHR + MCP Server Full Stack...
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml up --build -d
if errorlevel 1 (
    echo [ERROR] Failed to start services
    exit /b 1
)
echo [SUCCESS] Services started successfully!

REM Wait and check health
timeout /t 10 /nobreak >nul
call :check_health
call :show_urls
goto end

:test
echo [INFO] Running MCP integration tests...
timeout /t 30 /nobreak >nul
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml exec mcp-server python test_client.py --url http://localhost:8001 --test all
goto end

:logs
echo [INFO] Showing service logs...
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml logs -f
goto end

:stop
echo [INFO] Stopping services...
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml down
echo [SUCCESS] Services stopped
goto end

:restart
echo [INFO] Restarting services...
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml down
timeout /t 5 /nobreak >nul
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml up --build -d
timeout /t 10 /nobreak >nul
call :check_health
call :show_urls
goto end

:clean
echo [INFO] Cleaning up (removing containers and volumes)...
!DOCKER_COMPOSE_CMD! -f docker-compose.full-stack.yml down -v --remove-orphans
echo [SUCCESS] Cleanup completed
goto end

:health
call :check_health
goto end

:urls
call :show_urls
goto end

:check_health
echo [INFO] Checking service health...
curl -f http://localhost:8080/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] SmartHR Backend is healthy
) else (
    echo [WARNING] SmartHR Backend is not ready yet
)

curl -f http://localhost:8001/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] MCP Server is healthy
) else (
    echo [WARNING] MCP Server is not ready yet
)
exit /b 0

:show_urls
echo.
echo [INFO] Service URLs:
echo   SmartHR Backend:    http://localhost:8080
echo   SmartHR API Docs:   http://localhost:8080/docs
echo   MCP Server:         http://localhost:8001
echo   MCP Server Docs:    http://localhost:8001/docs
echo   PostgreSQL:         localhost:5432 (smarthr/smarthr123)
echo   Redis:              localhost:6379
echo.
echo [INFO] Health Check URLs:
echo   SmartHR Health:     http://localhost:8080/health
echo   MCP Health:         http://localhost:8001/health
echo   MCP Bridge Health:  http://localhost:8080/mcp/health
echo.
exit /b 0

:usage
echo Usage: %0 {start^|test^|logs^|stop^|restart^|clean^|health^|urls}
echo.
echo Commands:
echo   start    - Start all services (default)
echo   test     - Run MCP integration tests
echo   logs     - Show service logs
echo   stop     - Stop all services
echo   restart  - Restart all services
echo   clean    - Stop services and remove volumes
echo   health   - Check service health
echo   urls     - Show service URLs
exit /b 1

:end
endlocal
