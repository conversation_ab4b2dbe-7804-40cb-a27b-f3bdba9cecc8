"""
Monitoring and health check service for the FastAPI chatbot application.

This service provides comprehensive health checks, metrics collection,
and monitoring capabilities for production deployment.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from app.services.base import BaseService, ServiceHealth, ServiceStatus
from app.services.twilio_service import TwilioService
from app.services.session import SessionService
from app.services.stt import stt_service
from app.services.tts import tts_service
from app.utils.resilience import service_registry
from app.config import settings
from app.utils.logging import get_logger

logger = get_logger(__name__)


class HealthStatus(str, Enum):
    """Overall health status."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


@dataclass
class SystemMetrics:
    """System-wide metrics."""
    timestamp: datetime = field(default_factory=datetime.now)
    uptime_seconds: float = 0.0
    
    # Request metrics
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time_ms: float = 0.0
    
    # SMS metrics
    sms_messages_sent: int = 0
    sms_messages_received: int = 0
    sms_success_rate: float = 0.0
    
    # Voice metrics
    voice_calls_total: int = 0
    voice_calls_completed: int = 0
    voice_calls_failed: int = 0
    average_call_duration_seconds: float = 0.0
    
    # Session metrics
    active_sessions: int = 0
    total_sessions: int = 0
    average_session_duration_seconds: float = 0.0
    
    # Service health
    services_healthy: int = 0
    services_total: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "uptime_seconds": self.uptime_seconds,
            "requests": {
                "total": self.total_requests,
                "successful": self.successful_requests,
                "failed": self.failed_requests,
                "success_rate": self.successful_requests / max(self.total_requests, 1),
                "average_response_time_ms": self.average_response_time_ms,
            },
            "sms": {
                "messages_sent": self.sms_messages_sent,
                "messages_received": self.sms_messages_received,
                "success_rate": self.sms_success_rate,
            },
            "voice": {
                "calls_total": self.voice_calls_total,
                "calls_completed": self.voice_calls_completed,
                "calls_failed": self.voice_calls_failed,
                "success_rate": self.voice_calls_completed / max(self.voice_calls_total, 1),
                "average_duration_seconds": self.average_call_duration_seconds,
            },
            "sessions": {
                "active": self.active_sessions,
                "total": self.total_sessions,
                "average_duration_seconds": self.average_session_duration_seconds,
            },
            "services": {
                "healthy": self.services_healthy,
                "total": self.services_total,
                "health_rate": self.services_healthy / max(self.services_total, 1),
            }
        }


class MonitoringService(BaseService):
    """
    Service for monitoring application health and collecting metrics.
    
    Provides comprehensive health checks for all services and
    collects metrics for monitoring and alerting.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("monitoring", config)
        
        # Service instances
        self.twilio_service: Optional[TwilioService] = None
        self.session_service: Optional[SessionService] = None
        
        # Metrics tracking
        self.start_time = time.time()
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = config.get("max_history_size", 100)
        
        # Health check configuration
        self.health_check_interval = config.get("health_check_interval", 60)  # seconds
        self.health_check_timeout = config.get("health_check_timeout", 10)   # seconds
        
        # Request tracking
        self.request_times: List[float] = []
        self.max_request_times = 1000  # Keep last 1000 request times
    
    async def initialize(self) -> None:
        """Initialize the monitoring service."""
        await super().initialize()
        
        # Initialize service references
        try:
            from app.services.twilio_service import TwilioService
            from app.services.session import SessionService
            
            # These would be injected in a real application
            self.twilio_service = TwilioService(settings.twilio.__dict__)
            self.session_service = SessionService({})
            
        except Exception as e:
            self.logger.warning(f"Could not initialize service references: {e}")
        
        # Start background monitoring
        asyncio.create_task(self._background_monitoring())
        
        self.logger.info("Monitoring service initialized")
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the monitoring service."""
        return ServiceHealth(
            status=ServiceStatus.HEALTHY,
            message="Monitoring service is operational",
            details={
                "uptime_seconds": time.time() - self.start_time,
                "metrics_history_size": len(self.metrics_history),
                "last_health_check": datetime.now().isoformat(),
            }
        )
    
    async def get_system_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health status.
        
        Returns:
            Dictionary with overall health status and service details
        """
        service_healths = await self._check_all_services()
        
        # Determine overall health status
        healthy_services = sum(1 for h in service_healths.values() if h["status"] == "healthy")
        total_services = len(service_healths)
        
        if healthy_services == total_services:
            overall_status = HealthStatus.HEALTHY
        elif healthy_services >= total_services * 0.7:  # 70% threshold
            overall_status = HealthStatus.DEGRADED
        else:
            overall_status = HealthStatus.UNHEALTHY
        
        return {
            "status": overall_status.value,
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": time.time() - self.start_time,
            "services": service_healths,
            "summary": {
                "total_services": total_services,
                "healthy_services": healthy_services,
                "degraded_services": total_services - healthy_services,
                "health_percentage": (healthy_services / total_services * 100) if total_services > 0 else 0,
            }
        }
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive system metrics.
        
        Returns:
            Dictionary with current system metrics
        """
        metrics = await self._collect_metrics()
        return metrics.to_dict()
    
    async def get_readiness_status(self) -> Dict[str, Any]:
        """
        Get readiness status for Kubernetes/container orchestration.
        
        Returns:
            Dictionary with readiness status and checks
        """
        checks = {}
        all_ready = True
        
        # Check critical services
        critical_services = ["twilio", "session"]
        
        for service_name in critical_services:
            try:
                if service_name == "twilio" and self.twilio_service:
                    health = await self.twilio_service.health_check()
                    checks[service_name] = health.status.value == "healthy"
                elif service_name == "session" and self.session_service:
                    health = await self.session_service.health_check()
                    checks[service_name] = health.status.value == "healthy"
                else:
                    checks[service_name] = False
                
                if not checks[service_name]:
                    all_ready = False
                    
            except Exception as e:
                self.logger.error(f"Error checking readiness for {service_name}: {e}")
                checks[service_name] = False
                all_ready = False
        
        return {
            "ready": all_ready,
            "timestamp": datetime.now().isoformat(),
            "checks": checks,
        }
    
    def record_request(self, response_time_ms: float, success: bool = True) -> None:
        """
        Record a request for metrics tracking.
        
        Args:
            response_time_ms: Response time in milliseconds
            success: Whether the request was successful
        """
        self.request_times.append(response_time_ms)
        
        # Keep only recent request times
        if len(self.request_times) > self.max_request_times:
            self.request_times = self.request_times[-self.max_request_times:]
    
    async def _check_all_services(self) -> Dict[str, Dict[str, Any]]:
        """Check health of all services."""
        service_healths = {}
        
        # Check core services
        services_to_check = [
            ("twilio", self.twilio_service),
            ("session", self.session_service),
            ("stt", stt_service),
            ("tts", tts_service),
        ]
        
        for service_name, service_instance in services_to_check:
            try:
                if service_instance and hasattr(service_instance, 'health_check'):
                    health = await asyncio.wait_for(
                        service_instance.health_check(),
                        timeout=self.health_check_timeout
                    )
                    
                    service_healths[service_name] = {
                        "status": health.status.value,
                        "message": health.message,
                        "response_time_ms": health.response_time_ms,
                        "details": health.details or {},
                        "last_check": datetime.now().isoformat(),
                    }
                else:
                    service_healths[service_name] = {
                        "status": "unknown",
                        "message": "Service not available for health check",
                        "response_time_ms": 0,
                        "details": {},
                        "last_check": datetime.now().isoformat(),
                    }
                    
            except asyncio.TimeoutError:
                service_healths[service_name] = {
                    "status": "unhealthy",
                    "message": f"Health check timeout ({self.health_check_timeout}s)",
                    "response_time_ms": self.health_check_timeout * 1000,
                    "details": {},
                    "last_check": datetime.now().isoformat(),
                }
            except Exception as e:
                service_healths[service_name] = {
                    "status": "unhealthy",
                    "message": f"Health check error: {str(e)}",
                    "response_time_ms": 0,
                    "details": {"error": str(e)},
                    "last_check": datetime.now().isoformat(),
                }
        
        return service_healths
    
    async def _collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        metrics = SystemMetrics()
        
        # Basic system metrics
        metrics.uptime_seconds = time.time() - self.start_time
        
        # Request metrics
        if self.request_times:
            metrics.average_response_time_ms = sum(self.request_times) / len(self.request_times)
        
        # Session metrics
        if self.session_service:
            try:
                session_metrics = await self.session_service.get_session_metrics()
                metrics.active_sessions = session_metrics.active_sessions
                metrics.total_sessions = session_metrics.total_sessions
                metrics.average_session_duration_seconds = session_metrics.average_duration
                metrics.voice_calls_total = session_metrics.total_calls
                metrics.voice_calls_completed = session_metrics.completed_calls
                metrics.average_call_duration_seconds = session_metrics.average_call_duration
            except Exception as e:
                self.logger.error(f"Error collecting session metrics: {e}")
        
        # Service health metrics
        service_healths = await self._check_all_services()
        metrics.services_total = len(service_healths)
        metrics.services_healthy = sum(
            1 for h in service_healths.values() 
            if h["status"] == "healthy"
        )
        
        return metrics
    
    async def _background_monitoring(self) -> None:
        """Background task for periodic monitoring."""
        while True:
            try:
                # Collect and store metrics
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Keep history size manageable
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history = self.metrics_history[-self.max_history_size:]
                
                # Log periodic health summary
                self.logger.info(
                    "System health summary",
                    uptime_seconds=metrics.uptime_seconds,
                    active_sessions=metrics.active_sessions,
                    services_healthy=f"{metrics.services_healthy}/{metrics.services_total}",
                    avg_response_time_ms=metrics.average_response_time_ms
                )
                
                # Sleep until next check
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in background monitoring: {e}")
                await asyncio.sleep(60)  # Shorter sleep on error


# Global monitoring service instance
monitoring_service = MonitoringService({})
