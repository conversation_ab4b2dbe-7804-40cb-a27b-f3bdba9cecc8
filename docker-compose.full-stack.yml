services:
  # PostgreSQL Database
  postgres:
    image: pgvector/pgvector:pg15
    container_name: smarthr-postgres
    environment:
      POSTGRES_DB: smarthr
      POSTGRES_USER: smarthr
      POSTGRES_PASSWORD: smarthr123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./smarthr/smarthr-be/scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro
    networks:
      - smarthr-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smarthr -d smarthr"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: smarthr-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smarthr-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # SmartHR Backend
  smarthr-backend:
    build:
      context: ./smarthr/smarthr-be
      dockerfile: Dockerfile
    container_name: smarthr-backend
    ports:
      - "8080:8080"
    environment:
      # Database
      - DATABASE_URL=*********************************************/smarthr
      
      # Redis
      - REDIS_URL=redis://redis:6379/0
      
      # OpenAI/Azure OpenAI (configure as needed)
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT:-}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY:-}
      - AZURE_OPENAI_DEPLOYMENT_NAME=${AZURE_OPENAI_DEPLOYMENT_NAME:-gpt-4}
      - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=${AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS:-text-embedding-3-small}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION:-2023-05-15}
      
      # MCP Bridge Configuration
      - MCP_SERVER_URL=http://mcp-server:8001
      - MCP_SERVER_TIMEOUT=30
      
      # Application settings
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - smarthr-network
    volumes:
      - ./smarthr/smarthr-be/uploads:/app/uploads
      - ./smarthr/smarthr-be/logs:/app/logs
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # External MCP Server
  mcp-server:
    build:
      context: ./mcp_server
      dockerfile: Dockerfile
    container_name: smarthr-mcp-server
    ports:
      - "8001:8001"
    environment:
      # Server configuration
      - MCP_SERVER_HOST=0.0.0.0
      - MCP_SERVER_PORT=8001

      # SmartHR API configuration
      - SMARTHR_API_URL=http://smarthr-backend:8080

      # OpenAI/Azure OpenAI (only set if values exist)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_DEPLOYMENT_NAME=${AZURE_OPENAI_DEPLOYMENT_NAME}
      - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=${AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION}
    
    depends_on:
      smarthr-backend:
        condition: service_started
    networks:
      - smarthr-network
    volumes:
      - ./mcp_server/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  smarthr-network:
    driver: bridge
