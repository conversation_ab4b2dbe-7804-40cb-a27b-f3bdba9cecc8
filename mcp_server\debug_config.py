#!/usr/bin/env python3
"""
Debug script to test configuration loading
"""

import os
import sys

print("=== Environment Variables ===")
for key, value in sorted(os.environ.items()):
    if any(keyword in key.upper() for keyword in ['MCP', 'SMARTHR', 'OPENAI', 'AZURE', 'ALLOWED']):
        print(f"{key}='{value}'")

print(f"\n=== Specific Check ===")
print(f"ALLOWED_ORIGINS in os.environ: {'ALLOWED_ORIGINS' in os.environ}")
print(f"ALLOWED_ORIGINS value: '{os.environ.get('ALLOWED_ORIGINS', 'NOT_SET')}'")
print(f"ALLOWED_ORIGINS repr: {repr(os.environ.get('ALLOWED_ORIGINS', 'NOT_SET'))}")

print("\n=== Testing Configuration Loading ===")
try:
    from config import MCPServerConfig
    config = MCPServerConfig()
    print("✅ Configuration loaded successfully!")
    print(f"cors_origins: {config.cors_origins}")
    print(f"mcp_server_host: {config.mcp_server_host}")
    print(f"mcp_server_port: {config.mcp_server_port}")
except Exception as e:
    print(f"❌ Configuration failed: {e}")
    import traceback
    traceback.print_exc()
