"""
External MCP Server implementation.

This module provides the main FastAPI server for the external MCP server
that communicates with SmartHR via HTTP API and serves MCP clients.
"""

import json
import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, AsyncGenerator
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from config import get_mcp_config
from models import (
    ExtractCandidatesRequest, ExtractCandidatesResponse,
    GenerateQuestionsRequest, GenerateQuestionsResponse,
    EvaluateInterviewRequest, EvaluateInterviewResponse
)
from tools import extract_top_candidates, generate_interview_questions, evaluate_interview
from smarthr_client import get_smarthr_client

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting MCP Server...")
    
    # Validate configuration
    config = get_mcp_config()
    logger.info(f"MCP Server starting on {config.mcp_server_host}:{config.mcp_server_port}")
    
    # Check SmartHR connectivity
    smarthr_client = get_smarthr_client()
    if await smarthr_client.health_check():
        logger.info("SmartHR API connection verified")
    else:
        logger.warning("SmartHR API connection failed - some features may not work")
    
    yield
    
    # Shutdown
    logger.info("Shutting down MCP Server...")


class MCPServer:
    """External MCP Server implementation."""
    
    def __init__(self):
        self.config = get_mcp_config()
        self.app = FastAPI(
            title="SmartHR MCP Server",
            description="External MCP server for SmartHR integration",
            version="1.0.0",
            lifespan=lifespan
        )
        
        self._setup_middleware()
        self._setup_routes()
        self._tool_registry = self._initialize_tools()
    
    def _setup_middleware(self):
        """Setup FastAPI middleware."""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/")
        async def root():
            """Root endpoint with server information."""
            return {
                "name": "SmartHR MCP Server",
                "version": "1.0.0",
                "description": "External MCP server for SmartHR integration",
                "smarthr_api": self.config.smarthr_api_url,
                "tools": list(self._tool_registry.keys()),
                "status": "running",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            smarthr_client = get_smarthr_client()
            smarthr_healthy = await smarthr_client.health_check()
            
            return {
                "status": "healthy",
                "smarthr_api_status": "healthy" if smarthr_healthy else "unhealthy",
                "smarthr_api_url": self.config.smarthr_api_url,
                "tools_enabled": {
                    "extract_candidates": self.config.enable_extract_candidates,
                    "generate_questions": self.config.enable_generate_questions,
                    "evaluate_interview": self.config.enable_evaluate_interview
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        
        @self.app.get("/tools")
        async def list_tools():
            """List available MCP tools."""
            return {
                "tools": [
                    {
                        "name": "extract_top_candidates",
                        "description": "Extract and match top candidates for a job position using vectorization",
                        "enabled": self.config.enable_extract_candidates,
                        "method": "POST",
                        "endpoint": "/extract-candidates"
                    },
                    {
                        "name": "generate_interview_questions",
                        "description": "Generate interview questions with expected responses based on seniority levels",
                        "enabled": self.config.enable_generate_questions,
                        "method": "POST",
                        "endpoint": "/generate-questions"
                    },
                    {
                        "name": "evaluate_interview",
                        "description": "Evaluate interview transcripts using the four-agent evaluation system",
                        "enabled": self.config.enable_evaluate_interview,
                        "method": "POST",
                        "endpoint": "/evaluate-interview"
                    }
                ]
            }
        
        @self.app.get("/config")
        async def get_config():
            """Get server configuration (non-sensitive)."""
            return {
                "server_info": {
                    "host": self.config.mcp_server_host,
                    "port": self.config.mcp_server_port,
                    "debug": self.config.mcp_server_debug
                },
                "smarthr_api": {
                    "url": self.config.smarthr_api_url,
                    "timeout": self.config.smarthr_api_timeout
                },
                "tools_enabled": {
                    "extract_candidates": self.config.enable_extract_candidates,
                    "generate_questions": self.config.enable_generate_questions,
                    "evaluate_interview": self.config.enable_evaluate_interview
                },
                "limits": {
                    "max_candidates_per_search": self.config.max_candidates_per_search,
                    "default_questions_per_interview": self.config.default_questions_per_interview,
                    "max_questions_per_interview": self.config.max_questions_per_interview,
                    "vector_similarity_threshold": self.config.vector_similarity_threshold
                }
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """Get server statistics."""
            return {
                "status": "operational",
                "tools_available": len(self._tool_registry),
                "smarthr_api_url": self.config.smarthr_api_url,
                "last_updated": datetime.now(timezone.utc).isoformat()
            }
        
        # Tool endpoints
        @self.app.post("/extract-candidates", response_model=ExtractCandidatesResponse)
        async def extract_candidates_endpoint(request: ExtractCandidatesRequest):
            """Extract top candidates endpoint."""
            return await extract_top_candidates(request)
        
        @self.app.post("/generate-questions", response_model=GenerateQuestionsResponse)
        async def generate_questions_endpoint(request: GenerateQuestionsRequest):
            """Generate interview questions endpoint."""
            return await generate_interview_questions(request)
        
        @self.app.post("/evaluate-interview", response_model=EvaluateInterviewResponse)
        async def evaluate_interview_endpoint(request: EvaluateInterviewRequest):
            """Evaluate interview endpoint."""
            return await evaluate_interview(request)
        
        # SSE endpoint
        @self.app.post("/sse")
        async def sse_endpoint(request: Request):
            """Server-Sent Events endpoint for MCP tool invocations."""
            try:
                body = await request.json()
                tool_name = body.get("tool")
                arguments = body.get("arguments", {})
                request_id = body.get("request_id")
                
                if not tool_name:
                    raise HTTPException(status_code=400, detail="Tool name is required")
                
                return StreamingResponse(
                    self._handle_tool_invocation(tool_name, arguments, request_id),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                    }
                )
                
            except Exception as e:
                logger.error(f"Error in SSE endpoint: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def _initialize_tools(self) -> Dict[str, Any]:
        """Initialize tool registry."""
        return {
            "extract_top_candidates": extract_top_candidates,
            "generate_interview_questions": generate_interview_questions,
            "evaluate_interview": evaluate_interview
        }
    
    async def _handle_tool_invocation(self, tool_name: str, arguments: Dict[str, Any], 
                                    request_id: str = None) -> AsyncGenerator[str, None]:
        """Handle MCP tool invocation with SSE streaming."""
        try:
            # Send start event
            yield f"data: {json.dumps({'event': 'start', 'tool': tool_name, 'request_id': request_id})}\n\n"
            
            # Get tool function
            tool_func = self._tool_registry.get(tool_name)
            if not tool_func:
                yield f"data: {json.dumps({'event': 'error', 'error': f'Unknown tool: {tool_name}', 'request_id': request_id})}\n\n"
                return
            
            # Create request object based on tool
            if tool_name == "extract_top_candidates":
                request_obj = ExtractCandidatesRequest(**arguments)
            elif tool_name == "generate_interview_questions":
                request_obj = GenerateQuestionsRequest(**arguments)
            elif tool_name == "evaluate_interview":
                request_obj = EvaluateInterviewRequest(**arguments)
            else:
                yield f"data: {json.dumps({'event': 'error', 'error': f'Invalid tool: {tool_name}', 'request_id': request_id})}\n\n"
                return
            
            # Send progress event
            yield f"data: {json.dumps({'event': 'progress', 'message': f'Executing {tool_name}...', 'request_id': request_id})}\n\n"
            
            # Execute tool
            result = await tool_func(request_obj)
            
            # Send result event
            yield f"data: {json.dumps({'event': 'result', 'data': result.model_dump(), 'request_id': request_id})}\n\n"
            
            # Send completion event
            yield f"data: {json.dumps({'event': 'complete', 'tool': tool_name, 'request_id': request_id})}\n\n"
            
        except Exception as e:
            logger.error(f"Error in tool invocation: {e}")
            yield f"data: {json.dumps({'event': 'error', 'error': str(e), 'request_id': request_id})}\n\n"
    
    def run(self):
        """Run the MCP server."""
        import uvicorn
        uvicorn.run(
            self.app,
            host=self.config.mcp_server_host,
            port=self.config.mcp_server_port,
            log_level=self.config.log_level.lower()
        )


# Global server instance
mcp_server = MCPServer()


def get_mcp_server() -> MCPServer:
    """Get MCP server instance."""
    return mcp_server
