# FastAPI Chatbot with SMS and Voice Channels

A comprehensive FastAPI-based chatbot system supporting both SMS and Voice channels through Twilio integration, with Groq LLM, ElevenLabs TTS and STT capabilities.

## Architecture Overview

### SMS Channel
- Twilio SMS webhooks → FastAPI `/sms-webhook`
- Message processing through chatbot logic
- Response via Twilio REST API

### Voice Channel
- Twilio Voice webhooks → FastAPI `/voice-webhook`
- Real-time audio streaming via Twilio Media Streams
- WebSocket connection at `/media-stream`
- STT (Speech-to-Text) → Chatbot → TTS (Text-to-Speech) pipeline

## Project Structure

```
POC_chatbot/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py              # Configuration and environment variables
│   ├── api/
│   │   ├── __init__.py
│   │   ├── webhooks.py        # Twilio webhook endpoints
│   │   └── websockets.py      # WebSocket endpoints for media streams
│   ├── services/
│   │   ├── __init__.py
│   │   ├── base.py            # Base service classes
│   │   ├── chatbot.py         # Chatbot logic and conversation management
│   │   ├── twilio_service.py  # Twilio SMS, Voice, and Media Streams
│   │   ├── elevenlabs.py      # ElevenLabs TTS integration
│   │   ├── stt.py             # Speech-to-Text service
│   │   └── session.py         # Session and state management
│   ├── models/
│   │   ├── __init__.py
│   │   ├── requests.py        # Request/response models
│   │   ├── session.py         # Session data models
│   │   └── conversation.py    # Conversation state models
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── audio.py           # Audio processing utilities
│   │   ├── logging.py         # Logging configuration
│   │   └── validation.py      # Request validation utilities
│   └── middleware/
│       ├── __init__.py
│       ├── error_handler.py   # Global error handling
│       └── logging.py         # Request/response logging
├── static/
│   └── audio/                 # Static TTS audio files
├── tests/
│   ├── __init__.py
│   ├── test_webhooks.py       # Webhook endpoint tests
│   ├── test_services.py       # Service layer tests
│   └── test_websockets.py     # WebSocket tests
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── Dockerfile                # Docker configuration
├── docker-compose.yml        # Docker Compose setup
└── README.md                 # This file
```

## Quick Start

### 🖥️ Local Development

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd POC_chatbot
   pip install -r requirements.txt
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys:
   # - GROQ_API_KEY (get from https://console.groq.com/)
   # - ELEVENLABS_API_KEY (get from https://elevenlabs.io/)
   # - TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER
   ```

3. **Test Integration**
   ```bash
   python test_integration.py
   ```

4. **Run the Server**
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 5050 --reload
   ```

5. **Test the API**
   - Health check: `GET http://localhost:5050/health`
   - API docs: `http://localhost:5050/docs`

6. **Expose with ngrok (for development)**
   ```bash
   ngrok http 5050
   ```

### 🐳 Docker Deployment

1. **Setup Environment for Docker**
   ```bash
   cp .env.docker .env
   # Edit .env with your actual API keys
   ```

2. **Build and Run with Docker Compose**
   ```bash
   # Start just the API service
   docker-compose up -d chatbot-api

   # Or start with database and Redis
   docker-compose up -d chatbot-api postgres redis

   # For full production stack with monitoring
   docker-compose --profile production --profile monitoring up -d
   ```

3. **Check Container Status**
   ```bash
   docker-compose ps
   docker-compose logs chatbot-api
   ```

4. **Test the Dockerized API**
   - Health check: `GET http://localhost:8000/health`
   - API docs: `http://localhost:8000/docs`

5. **Configure Twilio Webhooks**
   - SMS: `https://your-ngrok-url.ngrok.app/sms-webhook`
   - Voice: `https://your-ngrok-url.ngrok.app/voice-webhook`

## Milestone Implementation Roadmap

### Milestone 1 - SMS Bot ✅
- [x] SMS webhook endpoint
- [x] Basic chatbot logic
- [x] Twilio SMS responses

### Milestone 2 - Voice (Basic) 🔄
- [ ] Voice webhook with TwiML
- [ ] Static TTS file playback
- [ ] Basic call handling

### Milestone 3 - Voice (Real-time) 🔄
- [ ] WebSocket Media Streams
- [ ] Real-time STT integration
- [ ] ElevenLabs TTS streaming
- [ ] Bidirectional audio pipeline

### Milestone 4 - Production Ready 🔄
- [ ] Session persistence
- [ ] Comprehensive error handling
- [ ] Monitoring and health checks
- [ ] Docker deployment

## API Endpoints

### Webhooks
- `POST /sms-webhook` - Twilio SMS webhook
- `POST /voice-webhook` - Twilio Voice webhook

### WebSockets
- `WS /media-stream` - Twilio Media Streams

### Health & Monitoring
- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint

## Configuration

See `.env.example` for all required environment variables including:
- Twilio credentials (Account SID, Auth Token, Phone Number)
- ElevenLabs API key for TTS and STT
- Groq API key for LLM
- Server configuration

## Development

### Running Tests

The application includes comprehensive test coverage for all major components.

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock httpx pytest-cov

# Run all tests
pytest

# Run with coverage report
pytest --cov=app --cov-report=html

# Run specific test categories
pytest tests/test_webhooks.py -v          # Webhook tests
pytest tests/test_services.py -v         # Service tests
pytest -m integration                    # Integration tests only

# Run tests with detailed output
pytest -v --tb=short
```

### Test Categories

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Webhook Tests**: Test Twilio webhook endpoints
- **Service Tests**: Test all service classes
- **API Tests**: Test FastAPI endpoints

### Code Style
```bash
black app/
flake8 app/
isort app/
mypy app/
```

### Docker Development
```bash
docker-compose up --build
```

## Production Deployment

See deployment documentation in the `docs/` directory for:
- Docker deployment
- Environment setup
- Monitoring configuration
- Security considerations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
