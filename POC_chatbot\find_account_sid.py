#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to help find your Twilio Account SID using your API Key
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def find_account_sid():
    """Try to find the Account SID using the API Key"""
    try:
        from twilio.rest import Client
        
        # Get credentials from .env
        api_key_sid = os.getenv("TWILIO_ACCOUNT_SID")  # This is actually the API Key SID (SK...)
        api_key_secret = os.getenv("TWILIO_AUTH_TOKEN")  # This is actually the API Key Secret
        
        print("🔍 Twilio Account SID Finder")
        print("=" * 50)
        print(f"API Key SID: {api_key_sid}")
        print(f"API Key Secret: {'*' * len(api_key_secret) if api_key_secret else 'None'}")
        print()
        
        if not api_key_sid or not api_key_secret:
            print("❌ Missing API Key credentials in .env file")
            return
        
        if not api_key_sid.startswith("SK"):
            print("❌ The TWILIO_ACCOUNT_SID in .env should start with 'SK' (API Key SID)")
            return
        
        print("📋 To find your Account SID:")
        print("1. Go to https://console.twilio.com/")
        print("2. Log in to your Twilio account")
        print("3. On the main dashboard, look for 'Account SID' (starts with AC...)")
        print("4. Copy that Account SID")
        print()
        print("🔧 Then update your .env file:")
        print("Add this line to your .env file:")
        print("TWILIO_ACTUAL_ACCOUNT_SID=AC_YOUR_ACCOUNT_SID_HERE")
        print()
        print("Or alternatively, replace the current TWILIO_ACCOUNT_SID with your AC... Account SID")
        print("and TWILIO_AUTH_TOKEN with your Auth Token from the Twilio Console.")
        print()
        
        # Try to make a test connection (this might fail but could give us more info)
        print("🧪 Testing API Key connection...")
        try:
            # We can't create a client without the Account SID, but we can show what we have
            print("✅ API Key format looks correct")
            print("📝 You need to provide the Account SID to complete the setup")
        except Exception as e:
            print(f"⚠️  Connection test info: {str(e)}")
        
    except ImportError:
        print("❌ Twilio library not installed. Run: pip install twilio")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    find_account_sid()
