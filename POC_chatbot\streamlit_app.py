"""
Streamlit Frontend for FastAPI Chatbot
A web interface to test and interact with the AI chatbot.
"""

import streamlit as st
import requests
import json
import uuid
import os
from datetime import datetime
from typing import Dict, List, Optional
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Streamlit page
st.set_page_config(
    page_title="AI Chatbot Test Interface",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configuration
FASTAPI_BASE_URL = "http://localhost:5050"
CHAT_ENDPOINT = f"{FASTAPI_BASE_URL}/api/v1/chat"
HEALTH_ENDPOINT = f"{FASTAPI_BASE_URL}/api/v1/chat/health"
SMS_WEBHOOK_ENDPOINT = f"{FASTAPI_BASE_URL}/sms-webhook"
VOICE_WEBHOOK_ENDPOINT = f"{FASTAPI_BASE_URL}/voice-webhook"

# Load Twilio configuration from environment
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER", "+***********")
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID", "**********************************")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN", "eb709432138d51ed5b28d6341f2f8147")

# Check if we're using API Key (starts with SK) or Account SID (starts with AC)
IS_API_KEY = TWILIO_ACCOUNT_SID.startswith("SK")
if IS_API_KEY:
    # For API Key authentication, we need the actual Account SID
    # This is a placeholder - you need to get your real Account SID from Twilio Console
    TWILIO_ACTUAL_ACCOUNT_SID = os.getenv("TWILIO_ACTUAL_ACCOUNT_SID", "AC_YOUR_ACCOUNT_SID_HERE")
    TWILIO_API_KEY_SID = TWILIO_ACCOUNT_SID  # This is the SK... value
    TWILIO_API_KEY_SECRET = TWILIO_AUTH_TOKEN  # This is the secret for the API key
else:
    TWILIO_ACTUAL_ACCOUNT_SID = TWILIO_ACCOUNT_SID

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "session_id" not in st.session_state:
    st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
if "api_status" not in st.session_state:
    st.session_state.api_status = "unknown"
if "sms_history" not in st.session_state:
    st.session_state.sms_history = []
if "voice_history" not in st.session_state:
    st.session_state.voice_history = []


def check_api_health() -> Dict:
    """Check if the FastAPI backend is healthy."""
    try:
        response = requests.get(HEALTH_ENDPOINT, timeout=5)
        if response.status_code == 200:
            return {"status": "healthy", "data": response.json()}
        else:
            return {"status": "unhealthy", "error": f"HTTP {response.status_code}"}
    except requests.exceptions.ConnectionError:
        return {"status": "disconnected", "error": "Cannot connect to FastAPI backend"}
    except requests.exceptions.Timeout:
        return {"status": "timeout", "error": "Request timed out"}
    except Exception as e:
        return {"status": "error", "error": str(e)}


def send_message(message: str, session_id: str) -> Optional[Dict]:
    """Send a message to the chatbot API."""
    try:
        payload = {
            "message": message,
            "session_id": session_id,
            "user_id": "streamlit_user"
        }
        
        response = requests.post(
            CHAT_ENDPOINT,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to FastAPI backend. Make sure it's running on http://localhost:5050")
        return None
    except requests.exceptions.Timeout:
        st.error("⏱️ Request timed out. The AI might be processing a complex response.")
        return None
    except Exception as e:
        st.error(f"❌ Error: {str(e)}")
        return None


def send_sms_via_twilio(to_number: str, message: str) -> Optional[Dict]:
    """Send an actual SMS message using Twilio API."""
    try:
        from twilio.rest import Client

        # Initialize Twilio client with proper authentication
        if IS_API_KEY:
            # Using API Key authentication
            if TWILIO_ACTUAL_ACCOUNT_SID == "AC_YOUR_ACCOUNT_SID_HERE":
                return {
                    "success": False,
                    "error": "Please set your actual Account SID in .env file as TWILIO_ACTUAL_ACCOUNT_SID=AC..."
                }
            client = Client(TWILIO_API_KEY_SID, TWILIO_API_KEY_SECRET, TWILIO_ACTUAL_ACCOUNT_SID)
        else:
            # Using Account SID and Auth Token
            client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        # Send SMS message
        message_obj = client.messages.create(
            body=message,
            from_=TWILIO_PHONE_NUMBER,
            to=to_number
        )

        return {
            "success": True,
            "message_sid": message_obj.sid,
            "status": message_obj.status,
            "from_number": TWILIO_PHONE_NUMBER,
            "to_number": to_number,
            "message": message,
            "date_created": str(message_obj.date_created)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def generate_ai_message(prompt: str = None) -> str:
    """Generate an AI message using the chat API."""
    try:
        if not prompt:
            prompt = "Generate a friendly greeting message for SMS"

        payload = {
            "message": prompt,
            "session_id": f"sms_gen_{uuid.uuid4().hex[:8]}",
            "user_id": "streamlit_sms_generator"
        }

        response = requests.post(
            CHAT_ENDPOINT,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            data = response.json()
            return data.get("response", "Hello! This is a test message from your AI chatbot.")
        else:
            return "Hello! This is a test message from your AI chatbot."

    except Exception as e:
        return f"Hello! This is a test message from your AI chatbot. (Error: {str(e)})"


def make_voice_call_via_twilio(to_number: str, message: str = None) -> Optional[Dict]:
    """Make an actual voice call using Twilio API."""
    try:
        from twilio.rest import Client

        # Initialize Twilio client with proper authentication
        if IS_API_KEY:
            # Using API Key authentication
            if TWILIO_ACTUAL_ACCOUNT_SID == "AC_YOUR_ACCOUNT_SID_HERE":
                return {
                    "success": False,
                    "error": "Please set your actual Account SID in .env file as TWILIO_ACTUAL_ACCOUNT_SID=AC..."
                }
            client = Client(TWILIO_API_KEY_SID, TWILIO_API_KEY_SECRET, TWILIO_ACTUAL_ACCOUNT_SID)
        else:
            # Using Account SID and Auth Token
            client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        # Create TwiML for the call
        if message:
            twiml_url = f"http://twimlets.com/message?Message%5B0%5D={requests.utils.quote(message)}"
        else:
            twiml_url = "http://twimlets.com/message?Message%5B0%5D=Hello%21%20This%20is%20a%20test%20call%20from%20your%20AI%20chatbot."

        # Make the call
        call = client.calls.create(
            url=twiml_url,
            to=to_number,
            from_=TWILIO_PHONE_NUMBER
        )

        return {
            "success": True,
            "call_sid": call.sid,
            "status": call.status,
            "from_number": TWILIO_PHONE_NUMBER,
            "to_number": to_number,
            "message": message or "Hello! This is a test call from your AI chatbot.",
            "date_created": str(call.date_created)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def display_message(role: str, content: str, timestamp: str = None):
    """Display a chat message."""
    with st.chat_message(role):
        st.write(content)
        if timestamp:
            st.caption(f"🕒 {timestamp}")


def render_sidebar():
    """Render the sidebar with common controls."""
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # API Status
        st.subheader("🔗 API Status")
        if st.button("🔄 Check Connection"):
            with st.spinner("Checking API health..."):
                health = check_api_health()
                st.session_state.api_status = health["status"]
        
        # Display status
        status_colors = {
            "healthy": "🟢",
            "unhealthy": "🟡", 
            "disconnected": "🔴",
            "timeout": "🟠",
            "error": "🔴",
            "unknown": "⚪"
        }
        
        status_color = status_colors.get(st.session_state.api_status, "⚪")
        st.write(f"{status_color} **Status:** {st.session_state.api_status.title()}")
        
        # Session Info
        st.subheader("📋 Session Info")
        st.write(f"**Session ID:** `{st.session_state.session_id}`")
        st.write(f"**Messages:** {len(st.session_state.messages)}")
        
        # Controls
        st.subheader("🎛️ Controls")
        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()
        
        if st.button("🔄 New Session"):
            st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
            st.session_state.messages = []
            st.rerun()
        
        # Settings
        st.subheader("⚙️ Settings")
        st.write(f"**FastAPI URL:** `{FASTAPI_BASE_URL}`")
        st.write("**Model:** Groq llama-3.3-70b-versatile")
        
        # Quick Test Messages
        st.subheader("🚀 Quick Tests")
        quick_messages = [
            "Hello! How are you?",
            "Tell me a joke",
            "What's the weather like?",
            "Explain quantum computing",
            "Write a haiku about AI"
        ]
        
        for msg in quick_messages:
            if st.button(f"💬 {msg[:20]}...", key=f"quick_{msg}"):
                st.session_state.quick_message = msg


def render_web_chat_tab():
    """Render the web chat interface."""
    st.subheader("💬 Web Chat Interface")
    
    # Display chat messages
    for message in st.session_state.messages:
        display_message(
            message["role"], 
            message["content"], 
            message.get("timestamp")
        )
    
    # Handle quick message
    if hasattr(st.session_state, 'quick_message'):
        user_input = st.session_state.quick_message
        delattr(st.session_state, 'quick_message')
    else:
        user_input = None
    
    # Chat input
    if prompt := st.chat_input("Type your message here...") or user_input:
        # Add user message to chat
        timestamp = datetime.now().strftime("%H:%M:%S")
        st.session_state.messages.append({
            "role": "user",
            "content": prompt,
            "timestamp": timestamp
        })
        
        # Display user message
        display_message("user", prompt, timestamp)
        
        # Send to API and get response
        with st.chat_message("assistant"):
            with st.spinner("🤔 AI is thinking..."):
                response_data = send_message(prompt, st.session_state.session_id)
            
            if response_data:
                ai_response = response_data["response"]
                response_time = response_data.get("timestamp", "")
                
                # Display AI response
                st.write(ai_response)
                if response_time:
                    st.caption(f"🕒 {response_time}")
                
                # Add to session state
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": ai_response,
                    "timestamp": response_time
                })
            else:
                st.error("Failed to get response from AI")
    
def render_sms_testing_tab():
    """Render the SMS testing interface."""
    st.subheader("📱 SMS Testing Interface")
    st.markdown("Send actual SMS messages from your AI chatbot to your phone using Twilio")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📤 Send AI-Generated SMS")

        st.info(f"📞 **Your Twilio Number:** {TWILIO_PHONE_NUMBER}")

        # SMS form
        with st.form("sms_test_form"):
            to_number = st.text_input("Your Phone Number", value="+1234567890", help="Your phone number to receive the SMS")

            # Message options
            message_type = st.radio(
                "Message Type:",
                ["AI Generated", "Custom Message"],
                help="Choose whether to generate an AI message or send a custom message"
            )

            if message_type == "AI Generated":
                ai_prompt = st.text_area(
                    "AI Prompt (optional)",
                    value="Send me a friendly greeting and tell me about your capabilities",
                    help="What should the AI say? Leave empty for a default greeting"
                )
                message_preview = st.empty()
            else:
                custom_message = st.text_area(
                    "Custom Message",
                    value="Hello! This is a test message from your AI chatbot.",
                    help="Enter your custom message"
                )

            # Generate AI message preview
            if message_type == "AI Generated":
                if st.form_submit_button("🤖 Preview AI Message", type="secondary"):
                    with st.spinner("Generating AI message..."):
                        preview_msg = generate_ai_message(ai_prompt if ai_prompt.strip() else None)
                        message_preview.text_area("AI Generated Message Preview:", value=preview_msg, height=100, disabled=True)

            submitted = st.form_submit_button("📱 Send SMS", type="primary")

            if submitted:
                if to_number:
                    # Determine message to send
                    if message_type == "AI Generated":
                        with st.spinner("Generating AI message..."):
                            message_to_send = generate_ai_message(ai_prompt if ai_prompt.strip() else None)
                    else:
                        message_to_send = custom_message

                    # Send SMS
                    with st.spinner("Sending SMS via Twilio..."):
                        result = send_sms_via_twilio(to_number, message_to_send)

                    if result and result.get("success"):
                        st.success(f"✅ SMS sent successfully!")
                        st.info(f"📱 **Message SID:** {result['message_sid']}")
                        st.info(f"📞 **From:** {result['from_number']} → **To:** {result['to_number']}")

                        # Add to history
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        st.session_state.sms_history.append({
                            "timestamp": timestamp,
                            "from": result['from_number'],
                            "to": result['to_number'],
                            "message": message_to_send,
                            "message_sid": result['message_sid'],
                            "status": result['status'],
                            "type": message_type
                        })

                        # Show message sent
                        st.subheader("📋 Message Sent")
                        st.text_area("SMS Content:", value=message_to_send, height=100, disabled=True)

                    else:
                        error_msg = result.get("error", "Unknown error") if result else "No response"
                        st.error(f"❌ Failed to send SMS: {error_msg}")
                else:
                    st.error("❌ Please enter your phone number")

    with col2:
        st.subheader("📜 SMS History")

        if st.session_state.sms_history:
            for i, sms in enumerate(reversed(st.session_state.sms_history[-10:])):  # Show last 10
                status_icon = "✅" if sms.get('status') in ['sent', 'delivered'] else "⏳"
                with st.expander(f"{status_icon} SMS {len(st.session_state.sms_history)-i} - {sms['timestamp']}"):
                    st.write(f"**Type:** {sms.get('type', 'Custom')}")
                    st.write(f"**From:** {sms['from']}")
                    st.write(f"**To:** {sms['to']}")
                    st.write(f"**Status:** {sms.get('status', 'Unknown')}")
                    if sms.get('message_sid'):
                        st.write(f"**Message SID:** {sms['message_sid']}")
                    st.text_area("Message:", value=sms['message'], height=80, disabled=True, key=f"msg_{i}")
        else:
            st.info("No SMS messages sent yet")

        if st.button("🗑️ Clear SMS History"):
            st.session_state.sms_history = []
            st.rerun()


def render_voice_testing_tab():
    """Render the voice testing interface."""
    st.subheader("📞 Voice Call Testing Interface")
    st.markdown("Make actual voice calls from your AI chatbot to your phone using Twilio")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📞 Make AI Voice Call")

        st.info(f"📞 **Your Twilio Number:** {TWILIO_PHONE_NUMBER}")

        # Voice form
        with st.form("voice_test_form"):
            to_number = st.text_input("Your Phone Number", value="+1234567890", help="Your phone number to receive the call")

            # Message options
            message_type = st.radio(
                "Call Message Type:",
                ["AI Generated", "Custom Message"],
                help="Choose the type of message for the voice call"
            )

            if message_type == "AI Generated":
                ai_prompt = st.text_area(
                    "AI Prompt (optional)",
                    value="Create a brief, friendly voice message introducing yourself as an AI assistant",
                    help="What should the AI say in the voice call?"
                )
            else:
                custom_message = st.text_area(
                    "Custom Voice Message",
                    value="Hello! This is a test call from your AI chatbot. Thank you for testing the voice functionality.",
                    help="Enter the message to be spoken during the call"
                )

            submitted = st.form_submit_button("📞 Make Voice Call", type="primary")

            if submitted:
                if to_number:
                    # Determine message for the call
                    if message_type == "AI Generated":
                        with st.spinner("Generating AI voice message..."):
                            voice_message = generate_ai_message(ai_prompt if ai_prompt.strip() else "Create a brief, friendly voice message introducing yourself as an AI assistant")
                    else:
                        voice_message = custom_message

                    # Make voice call
                    with st.spinner("Making voice call via Twilio..."):
                        result = make_voice_call_via_twilio(to_number, voice_message)

                    if result and result.get("success"):
                        st.success(f"✅ Voice call initiated successfully!")
                        st.info(f"📞 **Call SID:** {result['call_sid']}")
                        st.info(f"📞 **From:** {result['from_number']} → **To:** {result['to_number']}")
                        st.info("📱 **Your phone should ring shortly!**")

                        # Add to history
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        st.session_state.voice_history.append({
                            "timestamp": timestamp,
                            "from": result['from_number'],
                            "to": result['to_number'],
                            "message": voice_message,
                            "call_sid": result['call_sid'],
                            "status": result['status'],
                            "type": message_type
                        })

                        # Show message that will be spoken
                        st.subheader("📋 Voice Message")
                        st.text_area("Message to be spoken:", value=voice_message, height=100, disabled=True)

                    else:
                        error_msg = result.get("error", "Unknown error") if result else "No response"
                        st.error(f"❌ Failed to make voice call: {error_msg}")
                else:
                    st.error("❌ Please enter your phone number")

    with col2:
        st.subheader("📜 Voice Call History")

        if st.session_state.voice_history:
            for i, call in enumerate(reversed(st.session_state.voice_history[-10:])):  # Show last 10
                status_icon = "✅" if call.get('status') in ['queued', 'ringing', 'in-progress', 'completed'] else "⏳"
                with st.expander(f"{status_icon} Call {len(st.session_state.voice_history)-i} - {call['timestamp']}"):
                    st.write(f"**Type:** {call.get('type', 'Custom')}")
                    st.write(f"**From:** {call['from']}")
                    st.write(f"**To:** {call['to']}")
                    st.write(f"**Status:** {call.get('status', 'Unknown')}")
                    if call.get('call_sid'):
                        st.write(f"**Call SID:** {call['call_sid']}")
                    st.text_area("Voice Message:", value=call['message'], height=80, disabled=True, key=f"voice_msg_{i}")
        else:
            st.info("No voice calls made yet")

        if st.button("🗑️ Clear Voice History"):
            st.session_state.voice_history = []
            st.rerun()


def main():
    """Main Streamlit application with tabbed interface."""

    # Header
    st.title("🤖 AI Chatbot Test Interface")
    st.markdown("Comprehensive testing dashboard for FastAPI chatbot with SMS, Voice, and Web Chat")

    # Render sidebar
    render_sidebar()

    # Create tabs
    tab1, tab2, tab3 = st.tabs(["💬 Web Chat", "📱 SMS Testing", "📞 Voice Testing"])

    with tab1:
        render_web_chat_tab()

    with tab2:
        render_sms_testing_tab()

    with tab3:
        render_voice_testing_tab()

    # Footer
    st.markdown("---")
    st.markdown(
        "**💡 Tip:** Make sure your FastAPI server is running on `http://localhost:5050` "
        "before using this interface."
    )


if __name__ == "__main__":
    main()
