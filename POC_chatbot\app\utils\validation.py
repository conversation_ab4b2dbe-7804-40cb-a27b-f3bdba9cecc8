"""
Request validation utilities for the FastAPI chatbot application.

This module provides utilities for validating incoming requests,
particularly Twilio webhook signature validation for security.
"""

import base64
import hashlib
import hmac
from typing import Dict, Any, Optional
from urllib.parse import urlencode

from fastapi import Request, HTTPException, status

from app.config import settings
from app.utils.logging import get_logger

logger = get_logger(__name__)


class TwilioSignatureValidator:
    """
    Validator for Twilio webhook signatures.
    
    Validates that incoming webhook requests are actually from <PERSON>wi<PERSON>
    by verifying the X-Twilio-Signature header.
    """
    
    def __init__(self, auth_token: str):
        """
        Initialize the validator with Twilio auth token.
        
        Args:
            auth_token: Twilio auth token for signature validation
        """
        self.auth_token = auth_token
    
    def validate_signature(
        self, 
        url: str, 
        params: Dict[str, Any], 
        signature: str
    ) -> bool:
        """
        Validate a Twilio webhook signature.
        
        Args:
            url: The full URL of the webhook endpoint
            params: POST parameters from the request
            signature: X-Twilio-Signature header value
            
        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # Create the signature string
            signature_string = url
            
            # Sort parameters and append to signature string
            sorted_params = sorted(params.items())
            for key, value in sorted_params:
                signature_string += f"{key}{value}"
            
            # Create HMAC-SHA1 hash
            expected_signature = base64.b64encode(
                hmac.new(
                    self.auth_token.encode('utf-8'),
                    signature_string.encode('utf-8'),
                    hashlib.sha1
                ).digest()
            ).decode('utf-8')
            
            # Compare signatures
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Error validating Twilio signature: {e}")
            return False
    
    async def validate_request(self, request: Request) -> bool:
        """
        Validate a FastAPI request from Twilio.
        
        Args:
            request: FastAPI Request object
            
        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # Get the signature from headers
            signature = request.headers.get("X-Twilio-Signature")
            if not signature:
                logger.warning("Missing X-Twilio-Signature header")
                return False
            
            # Get the full URL
            url = str(request.url)
            
            # Get form data
            form_data = await request.form()
            params = dict(form_data)
            
            # Validate signature
            return self.validate_signature(url, params, signature)
            
        except Exception as e:
            logger.error(f"Error validating Twilio request: {e}")
            return False


def validate_phone_number(phone_number: str) -> bool:
    """
    Validate phone number format.
    
    Args:
        phone_number: Phone number to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not phone_number:
        return False
    
    # Basic validation - should start with + and contain only digits and +
    if not phone_number.startswith("+"):
        return False
    
    # Remove + and check if remaining characters are digits
    digits_only = phone_number[1:]
    if not digits_only.isdigit():
        return False
    
    # Check length (international phone numbers are typically 7-15 digits)
    if len(digits_only) < 7 or len(digits_only) > 15:
        return False
    
    return True


def validate_session_id(session_id: str) -> bool:
    """
    Validate session ID format.
    
    Args:
        session_id: Session ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not session_id:
        return False
    
    # Session ID should be alphanumeric and reasonable length
    if not session_id.replace("-", "").replace("_", "").isalnum():
        return False
    
    if len(session_id) < 3 or len(session_id) > 100:
        return False
    
    return True


def validate_message_content(content: str) -> bool:
    """
    Validate message content.
    
    Args:
        content: Message content to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not content:
        return False
    
    # Check length limits (SMS is typically 1600 chars max)
    if len(content) > 1600:
        return False
    
    # Content should not be just whitespace
    if not content.strip():
        return False
    
    return True


async def validate_twilio_webhook(request: Request) -> None:
    """
    Validate Twilio webhook request and raise HTTPException if invalid.
    
    Args:
        request: FastAPI Request object
        
    Raises:
        HTTPException: If validation fails
    """
    # Skip validation in development if disabled
    if not settings.twilio_validate_webhooks:
        logger.debug("Twilio webhook validation disabled")
        return
    
    # Create validator
    validator = TwilioSignatureValidator(settings.twilio_auth_token)
    
    # Validate signature
    is_valid = await validator.validate_request(request)
    
    if not is_valid:
        logger.warning(
            "Invalid Twilio webhook signature",
            url=str(request.url),
            headers=dict(request.headers)
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid webhook signature"
        )
    
    logger.debug("Twilio webhook signature validated successfully")


def sanitize_phone_number(phone_number: str) -> str:
    """
    Sanitize and normalize phone number.
    
    Args:
        phone_number: Raw phone number
        
    Returns:
        Sanitized phone number
    """
    if not phone_number:
        return ""
    
    # Remove all non-digit characters except +
    sanitized = "".join(c for c in phone_number if c.isdigit() or c == "+")
    
    # Ensure it starts with +
    if not sanitized.startswith("+"):
        # Assume US number if no country code
        if len(sanitized) == 10:
            sanitized = "+1" + sanitized
        elif len(sanitized) == 11 and sanitized.startswith("1"):
            sanitized = "+" + sanitized
        else:
            sanitized = "+" + sanitized
    
    return sanitized


def sanitize_message_content(content: str) -> str:
    """
    Sanitize message content.
    
    Args:
        content: Raw message content
        
    Returns:
        Sanitized message content
    """
    if not content:
        return ""
    
    # Strip whitespace
    sanitized = content.strip()
    
    # Remove null bytes and other control characters
    sanitized = "".join(c for c in sanitized if ord(c) >= 32 or c in ['\n', '\r', '\t'])
    
    # Limit length
    if len(sanitized) > 1600:
        sanitized = sanitized[:1600]
    
    return sanitized


def create_session_id(from_number: str, to_number: str, channel: str = "sms") -> str:
    """
    Create a session ID from phone numbers and channel.
    
    Args:
        from_number: Sender's phone number
        to_number: Recipient's phone number
        channel: Communication channel
        
    Returns:
        Generated session ID
    """
    # Create a consistent session ID based on the conversation participants
    # Use the smaller number first for consistency regardless of direction
    numbers = sorted([from_number, to_number])
    session_data = f"{numbers[0]}_{numbers[1]}_{channel}"
    
    # Create hash for shorter, consistent ID
    session_hash = hashlib.md5(session_data.encode()).hexdigest()[:16]
    
    return f"{channel}_{session_hash}"
