"""
Test cases for service classes.

This module contains comprehensive tests for all service classes
including Twilio, ChatBot, Session, STT, TTS, and Groq services.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.services.chatbot import ChatbotService, Message, ConversationContext
from app.services.session import SessionService
from app.services.twilio_service import TwilioService
from app.services.groq_service import GroqService
from app.models.session import (
    ConversationSession,
    VoiceCallSession,
    ChannelType,
    SessionStatus,
    CallStatus,
    MessageRole
)


class TestChatbotService:
    """Test cases for ChatbotService."""
    
    @pytest.fixture
    def chatbot_service(self):
        """Create chatbot service instance."""
        return ChatbotService({})
    
    @pytest.mark.asyncio
    async def test_process_message_new_session(self, chatbot_service):
        """Test processing message for new session."""
        await chatbot_service.initialize()
        
        response = await chatbot_service.process_message(
            session_id="test_session_1",
            message="Hello",
            channel="sms"
        )
        
        assert isinstance(response, str)
        assert len(response) > 0
        assert "test_session_1" in chatbot_service.conversations
    
    @pytest.mark.asyncio
    async def test_process_message_existing_session(self, chatbot_service):
        """Test processing message for existing session."""
        await chatbot_service.initialize()
        
        # First message
        await chatbot_service.process_message(
            session_id="test_session_2",
            message="Hello",
            channel="sms"
        )
        
        # Second message
        response = await chatbot_service.process_message(
            session_id="test_session_2",
            message="How are you?",
            channel="sms"
        )
        
        assert isinstance(response, str)
        context = chatbot_service.conversations["test_session_2"]
        assert len(context.messages) == 4  # 2 user + 2 assistant messages
    
    def test_conversation_context_expiration(self):
        """Test conversation context expiration."""
        context = ConversationContext(session_id="test")
        
        # Fresh context should not be expired
        assert not context.is_expired()
        
        # Set old timestamp
        context.last_activity = datetime.now() - timedelta(minutes=35)
        assert context.is_expired(timeout_minutes=30)
    
    def test_message_creation(self):
        """Test message creation and properties."""
        message = Message(
            content="Test message",
            role="user",
            metadata={"channel": "sms"}
        )
        
        assert message.content == "Test message"
        assert message.role == "user"
        assert message.metadata["channel"] == "sms"
        assert isinstance(message.timestamp, datetime)
    
    @pytest.mark.asyncio
    async def test_conversation_cleanup(self, chatbot_service):
        """Test automatic conversation cleanup."""
        await chatbot_service.initialize()
        
        # Create expired conversation
        session_id = "expired_session"
        await chatbot_service.process_message(session_id, "Hello", "sms")
        
        # Manually expire the conversation
        context = chatbot_service.conversations[session_id]
        context.last_activity = datetime.now() - timedelta(minutes=35)
        
        # Trigger cleanup
        chatbot_service._cleanup_expired_conversations()
        
        assert session_id not in chatbot_service.conversations


class TestSessionService:
    """Test cases for SessionService."""
    
    @pytest.fixture
    def session_service(self):
        """Create session service instance."""
        return SessionService({})
    
    @pytest.mark.asyncio
    async def test_create_session(self, session_service):
        """Test session creation."""
        await session_service.initialize()
        
        session = await session_service.create_session(
            session_id="test_session",
            channel=ChannelType.SMS,
            user_phone="+**********",
            bot_phone="+**********"
        )
        
        assert session.session_id == "test_session"
        assert session.channel == ChannelType.SMS
        assert session.user_phone == "+**********"
        assert session.status == SessionStatus.ACTIVE
    
    @pytest.mark.asyncio
    async def test_get_session(self, session_service):
        """Test session retrieval."""
        await session_service.initialize()
        
        # Create session
        await session_service.create_session(
            session_id="test_get_session",
            channel=ChannelType.SMS
        )
        
        # Retrieve session
        session = await session_service.get_session("test_get_session")
        
        assert session is not None
        assert session.session_id == "test_get_session"
    
    @pytest.mark.asyncio
    async def test_add_message_to_session(self, session_service):
        """Test adding messages to session."""
        await session_service.initialize()
        
        # Create session
        await session_service.create_session(
            session_id="test_message_session",
            channel=ChannelType.SMS
        )
        
        # Add message
        success = await session_service.add_message_to_session(
            session_id="test_message_session",
            content="Hello",
            role=MessageRole.USER,
            channel=ChannelType.SMS
        )
        
        assert success
        
        # Verify message was added
        session = await session_service.get_session("test_message_session")
        assert len(session.messages) == 1
        assert session.messages[0].content == "Hello"
    
    @pytest.mark.asyncio
    async def test_voice_call_session(self, session_service):
        """Test voice call session creation and management."""
        await session_service.initialize()
        
        # Create conversation session first
        await session_service.create_session(
            session_id="voice_session",
            channel=ChannelType.VOICE
        )
        
        # Create voice call session
        call_session = await session_service.create_voice_call_session(
            call_sid="CA123456789",
            session_id="voice_session",
            from_number="+**********",
            to_number="+**********",
            direction="inbound"
        )
        
        assert call_session.call_sid == "CA123456789"
        assert call_session.session_id == "voice_session"
        assert call_session.status == CallStatus.INITIATED
    
    @pytest.mark.asyncio
    async def test_session_expiration(self, session_service):
        """Test session expiration."""
        await session_service.initialize()
        
        # Create session
        session = await session_service.create_session(
            session_id="expiring_session",
            channel=ChannelType.SMS,
            timeout_minutes=1  # 1 minute timeout
        )
        
        # Session should not be expired initially
        assert not session.is_expired()
        
        # Manually set expiration time in the past
        session.expires_at = datetime.now() - timedelta(minutes=1)
        
        # Now it should be expired
        assert session.is_expired()


class TestTwilioService:
    """Test cases for TwilioService."""
    
    @pytest.fixture
    def twilio_config(self):
        """Twilio service configuration."""
        return {
            "account_sid": "test_account_sid",
            "auth_token": "test_auth_token",
            "phone_number": "+**********"
        }
    
    @pytest.fixture
    def twilio_service(self, twilio_config):
        """Create Twilio service instance."""
        return TwilioService(twilio_config)
    
    @pytest.mark.asyncio
    async def test_twilio_service_initialization(self, twilio_service):
        """Test Twilio service initialization."""
        with patch('twilio.rest.Client') as mock_client:
            mock_account = MagicMock()
            mock_account.status = "active"
            mock_client.return_value.api.accounts.return_value.fetch.return_value = mock_account
            
            await twilio_service.initialize()
            
            assert twilio_service.client is not None
            assert twilio_service.account_sid == "test_account_sid"
    
    @pytest.mark.asyncio
    async def test_send_message(self, twilio_service):
        """Test SMS message sending."""
        with patch('twilio.rest.Client') as mock_client:
            # Mock message response
            mock_message = MagicMock()
            mock_message.sid = "SM123456789"
            mock_message.status = "sent"
            mock_message.to = "+**********"
            mock_message.from_ = "+**********"
            mock_message.body = "Test message"
            mock_message.date_created = datetime.now()
            mock_message.price = None
            mock_message.error_code = None
            mock_message.error_message = None
            
            mock_client.return_value.messages.create.return_value = mock_message
            twilio_service.client = mock_client.return_value
            
            result = await twilio_service.send_message(
                to="+**********",
                message="Test message"
            )
            
            assert result["sid"] == "SM123456789"
            assert result["status"] == "sent"
            assert result["body"] == "Test message"
    
    def test_generate_twiml_response(self, twilio_service):
        """Test TwiML response generation."""
        twiml = twilio_service.generate_twiml_response(
            message="Hello, this is a test message"
        )
        
        assert isinstance(twiml, str)
        assert "Hello, this is a test message" in twiml
        assert "<Response>" in twiml
        assert "<Say>" in twiml
    
    def test_generate_sms_twiml_response(self, twilio_service):
        """Test SMS TwiML response generation."""
        twiml = twilio_service.generate_sms_twiml_response("Test SMS response")
        
        assert isinstance(twiml, str)
        assert "Test SMS response" in twiml
        assert "<Response>" in twiml
        assert "<Message>" in twiml
    
    @pytest.mark.asyncio
    async def test_health_check(self, twilio_service):
        """Test Twilio service health check."""
        with patch('twilio.rest.Client') as mock_client:
            mock_account = MagicMock()
            mock_account.status = "active"
            mock_client.return_value.api.accounts.return_value.fetch.return_value = mock_account
            
            twilio_service.client = mock_client.return_value
            
            health = await twilio_service.health_check()
            
            assert health.status.value == "healthy"
            assert "operational" in health.message.lower()


class TestServiceIntegration:
    """Integration tests for service interactions."""
    
    @pytest.mark.asyncio
    async def test_sms_flow_integration(self):
        """Test complete SMS flow integration."""
        # This would test the complete flow:
        # Webhook -> Session -> Chatbot -> Twilio Response
        
        # Mock all services
        session_service = AsyncMock(spec=SessionService)
        chatbot_service = AsyncMock(spec=ChatbotService)
        twilio_service = AsyncMock(spec=TwilioService)
        
        # Configure mocks
        session_service.create_session.return_value = ConversationSession(
            session_id="test_session",
            channel=ChannelType.SMS
        )
        chatbot_service.process_message.return_value = "AI response"
        twilio_service.generate_sms_twiml_response.return_value = "<Response><Message>AI response</Message></Response>"
        
        # Simulate SMS processing
        session = await session_service.create_session(
            session_id="test_integration",
            channel=ChannelType.SMS
        )
        
        response = await chatbot_service.process_message(
            session_id=session.session_id,
            message="Hello",
            channel="sms"
        )
        
        twiml = twilio_service.generate_sms_twiml_response(response)
        
        # Verify the flow
        assert session.session_id == "test_integration"
        assert response == "AI response"
        assert "AI response" in twiml
    
    @pytest.mark.asyncio
    async def test_voice_flow_integration(self):
        """Test complete voice flow integration."""
        # This would test the complete flow:
        # Voice Webhook -> Session -> TwiML Response
        
        session_service = AsyncMock(spec=SessionService)
        twilio_service = AsyncMock(spec=TwilioService)
        
        # Configure mocks
        call_session = VoiceCallSession(
            call_sid="CA123456789",
            session_id="voice_test",
            from_number="+**********",
            to_number="+**********",
            status=CallStatus.IN_PROGRESS,
            direction="inbound"
        )
        
        session_service.create_voice_call_session.return_value = call_session
        twilio_service.generate_twiml_response.return_value = "<Response><Say>Welcome</Say></Response>"
        
        # Simulate voice call processing
        call = await session_service.create_voice_call_session(
            call_sid="CA123456789",
            session_id="voice_test",
            from_number="+**********",
            to_number="+**********",
            direction="inbound"
        )
        
        twiml = twilio_service.generate_twiml_response(message="Welcome")
        
        # Verify the flow
        assert call.call_sid == "CA123456789"
        assert "Welcome" in twiml


class TestGroqService:
    """Test cases for GroqService."""

    @pytest.fixture
    def groq_config(self):
        """Groq service configuration."""
        return {
            "api_key": "test_groq_api_key",
            "model": "llama3-8b-8192",
            "temperature": 0.7,
            "max_tokens": 1024
        }

    @pytest.fixture
    def groq_service(self, groq_config):
        """Create Groq service instance."""
        return GroqService(groq_config)

    @pytest.mark.asyncio
    async def test_groq_service_initialization(self, groq_service):
        """Test Groq service initialization."""
        with patch('groq.AsyncGroq') as mock_groq:
            mock_models = MagicMock()
            mock_models.data = [
                MagicMock(id="llama3-8b-8192", object="model"),
                MagicMock(id="mixtral-8x7b-32768", object="model")
            ]
            mock_groq.return_value.models.list.return_value = mock_models

            await groq_service.initialize()

            assert groq_service.client is not None
            assert groq_service.model == "llama3-8b-8192"

    @pytest.mark.asyncio
    async def test_generate_response(self, groq_service):
        """Test response generation."""
        with patch('groq.AsyncGroq') as mock_groq:
            # Mock completion response
            mock_completion = MagicMock()
            mock_completion.choices = [MagicMock()]
            mock_completion.choices[0].message.content = "Test AI response"
            mock_completion.usage = MagicMock()
            mock_completion.usage.prompt_tokens = 10
            mock_completion.usage.completion_tokens = 5
            mock_completion.usage.total_tokens = 15

            mock_groq.return_value.chat.completions.create.return_value = mock_completion
            groq_service.client = mock_groq.return_value

            messages = [{"role": "user", "content": "Hello"}]
            response = await groq_service.generate_response(messages)

            assert response == "Test AI response"

    @pytest.mark.asyncio
    async def test_generate_simple_response(self, groq_service):
        """Test simple response generation."""
        with patch('groq.AsyncGroq') as mock_groq:
            mock_completion = MagicMock()
            mock_completion.choices = [MagicMock()]
            mock_completion.choices[0].message.content = "Simple response"

            mock_groq.return_value.chat.completions.create.return_value = mock_completion
            groq_service.client = mock_groq.return_value

            response = await groq_service.generate_simple_response("Test prompt")

            assert response == "Simple response"

    @pytest.mark.asyncio
    async def test_health_check(self, groq_service):
        """Test Groq service health check."""
        with patch('groq.AsyncGroq') as mock_groq:
            mock_models = MagicMock()
            mock_models.data = []
            mock_groq.return_value.models.list.return_value = mock_models

            groq_service.client = mock_groq.return_value

            health = await groq_service.health_check()

            assert health.status.value == "healthy"
            assert "operational" in health.message.lower()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
