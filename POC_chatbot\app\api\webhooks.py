"""
Webhook endpoints for Twilio SMS and Voice integration.

This module contains FastAPI endpoints that handle incoming webhooks
from Twilio for SMS messages and voice calls.
"""

from typing import Any, Dict
from fastapi import APIRouter, Request, Form, HTTPException, status, Depends
from fastapi.responses import Response
from twilio.twiml.messaging_response import MessagingResponse
from twilio.twiml.voice_response import VoiceResponse

from app.models.requests import TwilioSMSWebhookRequest, TwilioVoiceWebhookRequest
from app.services.chatbot import ChatbotService
from app.services.twilio_service import TwilioService
from app.utils.validation import (
    validate_twilio_webhook,
    sanitize_phone_number,
    sanitize_message_content,
    create_session_id
)
from app.utils.logging import get_logger
from app.config import settings, get_twilio_config

logger = get_logger(__name__)
router = APIRouter()

# Initialize services
chatbot_service = ChatbotService({})
twilio_service = TwilioService(get_twilio_config())


async def get_chatbot_service() -> ChatbotService:
    """Dependency to get chatbot service instance."""
    if not chatbot_service.is_initialized():
        await chatbot_service.initialize()
    return chatbot_service


async def get_twilio_service() -> TwilioService:
    """Dependency to get Twilio service instance."""
    # Skip initialization for testing if disabled
    enable_twilio_init = getattr(settings, 'enable_twilio_init', True)
    if enable_twilio_init and not twilio_service.is_initialized():
        await twilio_service.initialize()
    return twilio_service


@router.post("/sms-webhook")
async def sms_webhook(
    request: Request,
    chatbot: ChatbotService = Depends(get_chatbot_service),
    twilio: TwilioService = Depends(get_twilio_service),
    # Twilio SMS webhook parameters
    MessageSid: str = Form(...),
    AccountSid: str = Form(...),
    From: str = Form(...),
    To: str = Form(...),
    Body: str = Form(...),
    NumMedia: str = Form(default="0"),
    MediaContentType0: str = Form(default=None),
    MediaUrl0: str = Form(default=None),
    MessagingServiceSid: str = Form(default=None),
    NumSegments: str = Form(default="1"),
    ReferralNumMedia: str = Form(default="0"),
    SmsMessageSid: str = Form(default=None),
    SmsSid: str = Form(default=None),
    SmsStatus: str = Form(default=None),
) -> Response:
    """
    Handle incoming SMS messages from Twilio.
    
    This endpoint receives SMS webhook requests from Twilio,
    processes the message through the chatbot, and sends a response.
    """
    logger.info(
        "Received SMS webhook",
        message_sid=MessageSid,
        from_number=From,
        to_number=To,
        body_length=len(Body) if Body else 0,
        num_media=NumMedia
    )
    
    try:
        # Validate Twilio webhook signature
        await validate_twilio_webhook(request)
        
        # Sanitize input data
        from_number = sanitize_phone_number(From)
        to_number = sanitize_phone_number(To)
        message_body = sanitize_message_content(Body)
        
        # Validate required fields
        if not from_number or not to_number or not message_body:
            logger.error(
                "Invalid SMS webhook data",
                from_number=from_number,
                to_number=to_number,
                message_body_length=len(message_body)
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid webhook data"
            )
        
        # Check if SMS is enabled
        if not settings.enable_sms:
            logger.warning("SMS channel is disabled")
            return Response(content="", media_type="text/xml")
        
        # Create session ID for conversation tracking
        session_id = create_session_id(from_number, to_number, "sms")
        
        # Prepare user metadata
        user_metadata = {
            "message_sid": MessageSid,
            "account_sid": AccountSid,
            "num_media": NumMedia,
            "media_content_type": MediaContentType0,
            "media_url": MediaUrl0,
            "messaging_service_sid": MessagingServiceSid,
            "num_segments": NumSegments,
            "sms_status": SmsStatus,
        }
        
        # Process message through chatbot
        logger.info(
            "Processing SMS message",
            session_id=session_id,
            from_number=from_number,
            message_preview=message_body[:50] + "..." if len(message_body) > 50 else message_body
        )
        
        response_text = await chatbot.process_message(
            session_id=session_id,
            message=message_body,
            channel="sms",
            user_metadata=user_metadata
        )
        
        # Create TwiML response
        twiml_response = MessagingResponse()
        twiml_response.message(response_text)
        
        logger.info(
            "Sending SMS response",
            session_id=session_id,
            to_number=from_number,
            response_length=len(response_text)
        )
        
        # Return TwiML response
        return Response(
            content=str(twiml_response),
            media_type="text/xml"
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(
            "Error processing SMS webhook",
            error=str(e),
            message_sid=MessageSid,
            from_number=From,
            exc_info=True
        )
        
        # Return empty response to prevent Twilio retries
        return Response(content="", media_type="text/xml")


@router.post("/voice-webhook")
async def voice_webhook(
    request: Request,
    twilio: TwilioService = Depends(get_twilio_service),
    # Twilio Voice webhook parameters
    CallSid: str = Form(...),
    AccountSid: str = Form(...),
    From: str = Form(...),
    To: str = Form(...),
    CallStatus: str = Form(...),
    Direction: str = Form(...),
    ApiVersion: str = Form(default=None),
    CallerName: str = Form(default=None),
    CalledCity: str = Form(default=None),
    CalledState: str = Form(default=None),
    CalledZip: str = Form(default=None),
    CalledCountry: str = Form(default=None),
    CallerCity: str = Form(default=None),
    CallerState: str = Form(default=None),
    CallerZip: str = Form(default=None),
    CallerCountry: str = Form(default=None),
) -> Response:
    """
    Handle incoming voice calls from Twilio.
    
    This endpoint receives voice webhook requests from Twilio
    and returns TwiML instructions for handling the call.
    """
    logger.info(
        "Received voice webhook",
        call_sid=CallSid,
        from_number=From,
        to_number=To,
        call_status=CallStatus,
        direction=Direction
    )
    
    try:
        # Validate Twilio webhook signature
        await validate_twilio_webhook(request)
        
        # Check if voice is enabled
        if not settings.enable_voice:
            logger.warning("Voice channel is disabled")
            # Return TwiML to hang up
            response = VoiceResponse()
            response.say("Sorry, voice calls are not available at this time.")
            response.hangup()
            return Response(content=str(response), media_type="text/xml")
        
        # Sanitize input data
        from_number = sanitize_phone_number(From)
        to_number = sanitize_phone_number(To)
        
        # Create session ID for call tracking
        session_id = create_session_id(from_number, to_number, "voice")
        
        logger.info(
            "Processing voice call",
            session_id=session_id,
            call_sid=CallSid,
            from_number=from_number,
            call_status=CallStatus
        )
        
        # Generate TwiML response based on call status and features
        if CallStatus == "ringing":
            # Initial call - provide greeting and instructions
            response = VoiceResponse()
            
            if settings.enable_voice_streaming:
                # Use Media Streams for real-time conversation
                response.say(
                    "Hello! I'm your AI assistant. Please wait while I connect you for a conversation.",
                    voice="alice"
                )
                response.pause(length=1)
                
                # Connect to Media Streams WebSocket
                connect = response.connect()
                connect.stream(url=f"wss://{request.url.hostname}/media-stream")
                
            else:
                # Basic voice response without streaming
                response.say(
                    "Hello! I'm your AI assistant. Voice streaming is not available, but I'm here to help. "
                    "Please send me a text message instead.",
                    voice="alice"
                )
                response.hangup()
        
        else:
            # Handle other call statuses
            response = VoiceResponse()
            response.say("Thank you for calling. Goodbye!", voice="alice")
            response.hangup()
        
        logger.info(
            "Sending voice TwiML response",
            session_id=session_id,
            call_sid=CallSid,
            streaming_enabled=settings.enable_voice_streaming
        )
        
        return Response(content=str(response), media_type="text/xml")
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(
            "Error processing voice webhook",
            error=str(e),
            call_sid=CallSid,
            from_number=From,
            exc_info=True
        )
        
        # Return TwiML to hang up gracefully
        response = VoiceResponse()
        response.say("Sorry, there was an error. Please try again later.", voice="alice")
        response.hangup()
        return Response(content=str(response), media_type="text/xml")
