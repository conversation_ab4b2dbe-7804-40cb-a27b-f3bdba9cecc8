version: '3.8'

services:
  mcp-server:
    build: .
    ports:
      - "8001:8001"
    environment:
      # Server configuration
      - MCP_SERVER_HOST=0.0.0.0
      - MCP_SERVER_PORT=8001
      - MCP_SERVER_DEBUG=false
      
      # SmartHR API configuration
      - SMARTHR_API_URL=http://host.docker.internal:8080
      - SMARTHR_API_TIMEOUT=30
      # - SMARTHR_API_KEY=your_api_key_here
      
      # Tool configuration
      - ENABLE_EXTRACT_CANDIDATES=true
      - ENABLE_GENERATE_QUESTIONS=true
      - ENABLE_EVALUATE_INTERVIEW=true
      
      # Performance settings
      - VECTOR_SIMILARITY_THRESHOLD=0.3
      - MAX_CANDIDATES_PER_SEARCH=50
      - DEFAULT_QUESTIONS_PER_INTERVIEW=8
      - MAX_QUESTIONS_PER_INTERVIEW=15
      
      # Rate limiting
      - RATE_LIMIT_REQUESTS_PER_MINUTE=60
      - RATE_LIMIT_BURST_SIZE=10
      
      # Logging
      - LOG_LEVEL=INFO
      
      # Security
      - ALLOWED_ORIGINS=*
      
      # Performance
      - REQUEST_TIMEOUT_SECONDS=300
      - MAX_CONCURRENT_REQUESTS=10
    
    volumes:
      - ./logs:/app/logs
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: mcp-network
