# SmartHR + MCP Server Full Stack

Complete Docker setup for running SmartHR with the external MCP server.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │───▶│  SmartHR Bridge  │───▶│  External MCP   │
│                 │    │   Endpoints      │    │     Server      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   PostgreSQL     │    │   SmartHR API   │
                       │   + pgvector     │    │                 │
                       └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │      Redis       │
                       │                  │
                       └──────────────────┘
```

## 🚀 Quick Start

### 1. Prerequisites

- Docker and Docker Compose installed
- OpenAI API key or Azure OpenAI credentials

### 2. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# At minimum, configure OpenAI/Azure OpenAI settings
```

### 3. Start Everything

**Linux/Mac:**
```bash
./run-smarthr-mcp.sh start
```

**Windows:**
```cmd
run-smarthr-mcp.bat start
```

### 4. Verify Services

The script will show you all service URLs:
- SmartHR Backend: http://localhost:8080
- MCP Server: http://localhost:8001
- API Documentation: http://localhost:8080/docs

## 📋 Services

| Service | Port | Description |
|---------|------|-------------|
| SmartHR Backend | 8080 | Main application with bridge endpoints |
| MCP Server | 8001 | External MCP server |
| PostgreSQL | 5432 | Database with pgvector extension |
| Redis | 6379 | Cache and session storage |

## 🔧 Configuration

### Required Environment Variables

Edit `.env` file with your settings:

```env
# Azure OpenAI (Recommended)
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key-here
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=text-embedding-3-small

# OR Direct OpenAI
OPENAI_API_KEY=sk-your-openai-api-key-here
```

### Optional Settings

All other settings have sensible defaults but can be customized:

```env
# Database (uses Docker defaults)
DATABASE_URL=*********************************************/smarthr

# MCP Configuration
MCP_SERVER_URL=http://mcp-server:8001
ENABLE_EXTRACT_CANDIDATES=true
ENABLE_GENERATE_QUESTIONS=true
ENABLE_EVALUATE_INTERVIEW=true
```

## 🎮 Usage Commands

### Linux/Mac Commands

```bash
# Start all services
./run-smarthr-mcp.sh start

# Run integration tests
./run-smarthr-mcp.sh test

# View logs
./run-smarthr-mcp.sh logs

# Check health
./run-smarthr-mcp.sh health

# Show service URLs
./run-smarthr-mcp.sh urls

# Stop services
./run-smarthr-mcp.sh stop

# Restart services
./run-smarthr-mcp.sh restart

# Clean up (remove volumes)
./run-smarthr-mcp.sh clean
```

### Windows Commands

```cmd
REM Start all services
run-smarthr-mcp.bat start

REM Run integration tests
run-smarthr-mcp.bat test

REM View logs
run-smarthr-mcp.bat logs

REM Check health
run-smarthr-mcp.bat health

REM Show service URLs
run-smarthr-mcp.bat urls

REM Stop services
run-smarthr-mcp.bat stop

REM Restart services
run-smarthr-mcp.bat restart

REM Clean up (remove volumes)
run-smarthr-mcp.bat clean
```

## 🧪 Testing the MCP Integration

### Automated Tests

```bash
# Run all MCP tests
./run-smarthr-mcp.sh test
```

### Manual Testing

**Health Checks:**
```bash
curl http://localhost:8080/health          # SmartHR health
curl http://localhost:8001/health          # MCP server health
curl http://localhost:8080/mcp/health      # Bridge health
```

**Extract Candidates:**
```bash
curl -X POST http://localhost:8080/mcp/extract-candidates \
  -H "Content-Type: application/json" \
  -d '{
    "position": {
      "position_id": "pos_123",
      "title": "Senior Python Developer",
      "description": "Looking for experienced Python developer",
      "required_skills": ["Python", "FastAPI", "PostgreSQL"],
      "seniority_level": "senior"
    },
    "max_candidates": 5
  }'
```

**Generate Questions:**
```bash
curl -X POST http://localhost:8080/mcp/generate-questions \
  -H "Content-Type: application/json" \
  -d '{
    "position_id": "pos_123",
    "num_questions": 8,
    "target_seniority": "senior"
  }'
```

**Evaluate Interview:**
```bash
curl -X POST http://localhost:8080/mcp/evaluate-interview \
  -H "Content-Type: application/json" \
  -d '{
    "interview_id": "int_456",
    "transcript": "Q: Tell me about Python... A: Python is...",
    "position_id": "pos_123",
    "use_four_agent_system": true
  }'
```

## 📊 Monitoring

### Health Check Endpoints

- **SmartHR**: `GET http://localhost:8080/health`
- **MCP Server**: `GET http://localhost:8001/health`
- **Bridge**: `GET http://localhost:8080/mcp/health`

### Service Logs

```bash
# View all logs
./run-smarthr-mcp.sh logs

# View specific service logs
docker-compose -f docker-compose.full-stack.yml logs smarthr-backend
docker-compose -f docker-compose.full-stack.yml logs mcp-server
docker-compose -f docker-compose.full-stack.yml logs postgres
```

### Database Access

```bash
# Connect to PostgreSQL
docker-compose -f docker-compose.full-stack.yml exec postgres psql -U smarthr -d smarthr

# Connect to Redis
docker-compose -f docker-compose.full-stack.yml exec redis redis-cli
```

## 🔍 Troubleshooting

### Common Issues

1. **Services not starting**
   - Check Docker is running: `docker info`
   - Check .env file exists and has OpenAI credentials
   - Check ports 8080, 8001, 5432, 6379 are available

2. **MCP Server connection failed**
   - Check MCP server health: `curl http://localhost:8001/health`
   - Check bridge health: `curl http://localhost:8080/mcp/health`
   - Verify MCP_SERVER_URL in environment

3. **Database connection issues**
   - Wait for PostgreSQL to be ready (check logs)
   - Verify DATABASE_URL configuration
   - Check if pgvector extension is loaded

4. **OpenAI/Azure OpenAI errors**
   - Verify API keys in .env file
   - Check deployment names match your Azure setup
   - Ensure sufficient API quota

### Debug Commands

```bash
# Check service status
docker-compose -f docker-compose.full-stack.yml ps

# Check service health
./run-smarthr-mcp.sh health

# View detailed logs
./run-smarthr-mcp.sh logs

# Restart specific service
docker-compose -f docker-compose.full-stack.yml restart smarthr-backend
docker-compose -f docker-compose.full-stack.yml restart mcp-server
```

### Reset Everything

```bash
# Complete cleanup and restart
./run-smarthr-mcp.sh clean
./run-smarthr-mcp.sh start
```

## 📁 Project Structure

```
├── docker-compose.full-stack.yml    # Main Docker Compose file
├── .env.example                     # Environment template
├── run-smarthr-mcp.sh              # Linux/Mac startup script
├── run-smarthr-mcp.bat             # Windows startup script
├── smarthr/smarthr-be/             # SmartHR backend
│   ├── routes/routes_mcp.py        # Bridge endpoints
│   └── Dockerfile                  # SmartHR Docker config
└── mcp_server/                     # External MCP server
    ├── main.py                     # MCP server entry point
    ├── server.py                   # FastAPI server
    ├── tools.py                    # MCP tools implementation
    ├── test_client.py              # Test utilities
    └── Dockerfile                  # MCP server Docker config
```

## 🎯 Next Steps

1. **Configure Environment**: Edit `.env` with your OpenAI credentials
2. **Start Services**: Run `./run-smarthr-mcp.sh start`
3. **Test Integration**: Run `./run-smarthr-mcp.sh test`
4. **Access APIs**: Visit http://localhost:8080/docs for API documentation
5. **Monitor Health**: Use health check endpoints for monitoring

The full stack is now ready for development and testing!
