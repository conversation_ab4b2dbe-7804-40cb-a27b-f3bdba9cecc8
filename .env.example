# SmartHR + MCP Server Full Stack Configuration
# Copy this file to .env and configure your settings

# ================================
# OpenAI Configuration
# ================================
# Choose ONE of the following options:

# Option 1: Direct OpenAI
# OPENAI_API_KEY=sk-your-openai-api-key-here

# Option 2: Azure OpenAI (Recommended)
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-key-here
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=text-embedding-3-small
AZURE_OPENAI_API_VERSION=2023-05-15

# ================================
# Database Configuration (Optional - uses Docker defaults)
# ================================
# DATABASE_URL=postgresql://smarthr:smarthr123@localhost:5432/smarthr
# REDIS_URL=redis://localhost:6379/0

# ================================
# SmartHR Application Settings (Optional)
# ================================
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=INFO

# ================================
# MCP Server Settings (Optional - uses defaults)
# ================================
# MCP_SERVER_URL=http://localhost:8001
# MCP_SERVER_TIMEOUT=30
# ENABLE_EXTRACT_CANDIDATES=true
# ENABLE_GENERATE_QUESTIONS=true
# ENABLE_EVALUATE_INTERVIEW=true
# VECTOR_SIMILARITY_THRESHOLD=0.3
# MAX_CANDIDATES_PER_SEARCH=50
