# Docker Environment Configuration for FastAPI Chatbot
# Copy this file to .env when running with Docker Compose

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate a secure random string for production
SECRET_KEY=your_secret_key_here_change_this_in_production

# =============================================================================
# TWILIO CONFIGURATION
# =============================================================================
# Replace with your actual Twilio credentials
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=+**********
TWILIO_VALIDATE_WEBHOOKS=true

# =============================================================================
# GROQ CONFIGURATION
# =============================================================================
# Your Groq API Key
GROQ_API_KEY=gsk_your_groq_api_key_here
GROQ_MODEL=llama-3.3-70b-versatile
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=1024

# =============================================================================
# ELEVENLABS CONFIGURATION
# =============================================================================
# Your ElevenLabs API Key
ELEVENLABS_API_KEY=sk_your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM
ELEVENLABS_MODEL_ID=eleven_monolingual_v1
ELEVENLABS_STABILITY=0.5
ELEVENLABS_SIMILARITY_BOOST=0.5
ELEVENLABS_STYLE=0.0

# ElevenLabs STT Configuration
ELEVENLABS_STT_MODEL=eleven_multilingual_v2

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGINS=*
