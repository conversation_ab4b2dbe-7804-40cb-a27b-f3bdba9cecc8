"""
Request and response models for the FastAPI chatbot application.

This module contains Pydantic models for validating and serializing
HTTP requests and responses for webhooks and API endpoints.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime


class TwilioSMSWebhookRequest(BaseModel):
    """Model for Twilio SMS webhook requests."""
    
    # Required Twilio SMS parameters
    MessageSid: str = Field(..., description="Unique message identifier")
    AccountSid: str = Field(..., description="Twilio account SID")
    From: str = Field(..., description="Sender's phone number")
    To: str = Field(..., description="Recipient's phone number")
    Body: str = Field(..., description="Message body content")
    
    # Optional Twilio SMS parameters
    NumMedia: Optional[str] = Field(default="0", description="Number of media attachments")
    MediaContentType0: Optional[str] = Field(default=None, description="First media content type")
    MediaUrl0: Optional[str] = Field(default=None, description="First media URL")
    
    # Additional Twilio parameters
    MessagingServiceSid: Optional[str] = Field(default=None, description="Messaging service SID")
    NumSegments: Optional[str] = Field(default="1", description="Number of message segments")
    ReferralNumMedia: Optional[str] = Field(default="0", description="Number of referral media")
    SmsMessageSid: Optional[str] = Field(default=None, description="SMS message SID")
    SmsSid: Optional[str] = Field(default=None, description="SMS SID")
    SmsStatus: Optional[str] = Field(default=None, description="SMS status")
    
    @validator("From", "To")
    def validate_phone_numbers(cls, v):
        """Validate phone number format."""
        if not v.startswith("+"):
            raise ValueError("Phone numbers must start with '+'")
        return v
    
    @validator("NumMedia", pre=True)
    def convert_num_media(cls, v):
        """Convert NumMedia to string if it's an integer."""
        return str(v) if v is not None else "0"


class TwilioVoiceWebhookRequest(BaseModel):
    """Model for Twilio Voice webhook requests."""
    
    # Required Twilio Voice parameters
    CallSid: str = Field(..., description="Unique call identifier")
    AccountSid: str = Field(..., description="Twilio account SID")
    From: str = Field(..., description="Caller's phone number")
    To: str = Field(..., description="Called phone number")
    CallStatus: str = Field(..., description="Call status")
    Direction: str = Field(..., description="Call direction (inbound/outbound)")
    
    # Optional Twilio Voice parameters
    ApiVersion: Optional[str] = Field(default=None, description="Twilio API version")
    CallerName: Optional[str] = Field(default=None, description="Caller's name")
    CalledCity: Optional[str] = Field(default=None, description="Called city")
    CalledState: Optional[str] = Field(default=None, description="Called state")
    CalledZip: Optional[str] = Field(default=None, description="Called ZIP code")
    CalledCountry: Optional[str] = Field(default=None, description="Called country")
    CallerCity: Optional[str] = Field(default=None, description="Caller's city")
    CallerState: Optional[str] = Field(default=None, description="Caller's state")
    CallerZip: Optional[str] = Field(default=None, description="Caller's ZIP code")
    CallerCountry: Optional[str] = Field(default=None, description="Caller's country")
    
    @validator("From", "To")
    def validate_phone_numbers(cls, v):
        """Validate phone number format."""
        if not v.startswith("+"):
            raise ValueError("Phone numbers must start with '+'")
        return v


class ChatbotRequest(BaseModel):
    """Model for chatbot API requests."""
    
    message: str = Field(..., description="User message content")
    session_id: str = Field(..., description="Session identifier")
    channel: Optional[str] = Field(default="api", description="Communication channel")
    user_metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional user data")


class ChatbotResponse(BaseModel):
    """Model for chatbot API responses."""
    
    response: str = Field(..., description="Generated response message")
    session_id: str = Field(..., description="Session identifier")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional response data")


class HealthCheckResponse(BaseModel):
    """Model for health check responses."""
    
    status: str = Field(..., description="Service status")
    environment: str = Field(..., description="Environment name")
    version: str = Field(..., description="Application version")
    timestamp: datetime = Field(default_factory=datetime.now, description="Check timestamp")


class ReadinessCheckResponse(BaseModel):
    """Model for readiness check responses."""
    
    status: str = Field(..., description="Readiness status")
    checks: Dict[str, str] = Field(..., description="Individual service checks")
    timestamp: datetime = Field(default_factory=datetime.now, description="Check timestamp")


class ErrorResponse(BaseModel):
    """Model for error responses."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    type: str = Field(..., description="Error category")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")


class WebSocketMessage(BaseModel):
    """Model for WebSocket messages."""
    
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")


class TwilioMediaStreamEvent(BaseModel):
    """Model for Twilio Media Stream events."""
    
    event: str = Field(..., description="Event type (connected, start, media, stop)")
    sequenceNumber: Optional[str] = Field(default=None, description="Sequence number")
    media: Optional[Dict[str, Any]] = Field(default=None, description="Media data")
    start: Optional[Dict[str, Any]] = Field(default=None, description="Stream start data")
    streamSid: Optional[str] = Field(default=None, description="Stream SID")


class ConversationHistoryResponse(BaseModel):
    """Model for conversation history responses."""
    
    session_id: str = Field(..., description="Session identifier")
    messages: List[Dict[str, Any]] = Field(..., description="Conversation messages")
    created_at: datetime = Field(..., description="Conversation creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    message_count: int = Field(..., description="Total message count")


class ServiceStatusResponse(BaseModel):
    """Model for service status responses."""
    
    service_name: str = Field(..., description="Service name")
    status: str = Field(..., description="Service status")
    message: str = Field(..., description="Status message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional details")
    response_time_ms: Optional[float] = Field(default=None, description="Response time in milliseconds")
    timestamp: datetime = Field(default_factory=datetime.now, description="Status check timestamp")
