"""
Request/response logging middleware for the FastAPI chatbot application.

This middleware logs incoming requests and outgoing responses for
debugging and monitoring purposes.
"""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logging import get_logger

logger = get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to log HTTP requests and responses.
    
    Logs request details, response status, and processing time
    for monitoring and debugging purposes.
    """
    
    def __init__(self, app, log_body: bool = False):
        """
        Initialize the logging middleware.
        
        Args:
            app: The FastAPI application
            log_body: Whether to log request/response bodies (default: False)
        """
        super().__init__(app)
        self.log_body = log_body
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and log details.
        
        Args:
            request: The incoming HTTP request
            call_next: The next middleware or route handler
            
        Returns:
            HTTP response from the handler
        """
        start_time = time.time()
        
        # Log incoming request
        await self._log_request(request)
        
        # Process the request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log outgoing response
        await self._log_response(request, response, process_time)
        
        return response
    
    async def _log_request(self, request: Request) -> None:
        """
        Log incoming request details.
        
        Args:
            request: The HTTP request to log
        """
        # Extract request details
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        content_type = request.headers.get("content-type", "unknown")
        content_length = request.headers.get("content-length", "0")
        
        # Log request
        logger.info(
            "Incoming request",
            method=request.method,
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=client_ip,
            user_agent=user_agent,
            content_type=content_type,
            content_length=content_length,
        )
        
        # Log request body if enabled (be careful with sensitive data)
        if self.log_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                # Only log for specific content types to avoid binary data
                if content_type and "application/json" in content_type:
                    body = await request.body()
                    if body:
                        logger.debug(
                            "Request body",
                            path=request.url.path,
                            body=body.decode("utf-8")[:1000]  # Limit body size
                        )
            except Exception as e:
                logger.warning(f"Could not log request body: {e}")
    
    async def _log_response(
        self, 
        request: Request, 
        response: Response, 
        process_time: float
    ) -> None:
        """
        Log outgoing response details.
        
        Args:
            request: The original HTTP request
            response: The HTTP response
            process_time: Time taken to process the request
        """
        # Log response
        logger.info(
            "Outgoing response",
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            process_time_ms=round(process_time * 1000, 2),
            response_headers=dict(response.headers),
        )
        
        # Log slow requests
        if process_time > 1.0:  # More than 1 second
            logger.warning(
                "Slow request detected",
                method=request.method,
                path=request.url.path,
                process_time_ms=round(process_time * 1000, 2),
            )
        
        # Log error responses
        if response.status_code >= 400:
            logger.warning(
                "Error response",
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
            )
