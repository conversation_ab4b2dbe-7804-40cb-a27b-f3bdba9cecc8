# SmartHR MCP Server Configuration
# Copy this file to .env and configure your settings

# ================================
# Server Configuration
# ================================
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8001
MCP_SERVER_DEBUG=false

# ================================
# SmartHR API Configuration
# ================================
SMARTHR_API_URL=http://localhost:8080
SMARTHR_API_TIMEOUT=30
# SMARTHR_API_KEY=your_api_key_here

# ================================
# OpenAI Configuration (Optional)
# ================================
# For direct OpenAI usage (if not using SmartHR's embedding service)
# OPENAI_API_KEY=your_openai_api_key_here

# For Azure OpenAI
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_KEY=your_azure_openai_key_here
# AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
# AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=text-embedding-3-small
# AZURE_OPENAI_API_VERSION=2023-05-15

# ================================
# Tool Configuration
# ================================
ENABLE_EXTRACT_CANDIDATES=true
ENABLE_GENERATE_QUESTIONS=true
ENABLE_EVALUATE_INTERVIEW=true

# ================================
# Performance Settings
# ================================
VECTOR_SIMILARITY_THRESHOLD=0.3
MAX_CANDIDATES_PER_SEARCH=50
DEFAULT_QUESTIONS_PER_INTERVIEW=8
MAX_QUESTIONS_PER_INTERVIEW=15

# ================================
# Rate Limiting
# ================================
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# ================================
# Logging Configuration
# ================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ================================
# Security Configuration
# ================================
ALLOWED_ORIGINS=*

# ================================
# Performance Configuration
# ================================
REQUEST_TIMEOUT_SECONDS=300
MAX_CONCURRENT_REQUESTS=10
