"""
Audio processing utilities for the FastAPI chatbot application.

This module provides utilities for audio format conversion, TTS file generation,
and audio processing for voice interactions.
"""

import os
import hashlib
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path

from app.utils.logging import get_logger
from app.config import settings

logger = get_logger(__name__)


class AudioFileManager:
    """
    Manager for static audio files and TTS generation.
    
    Handles creation, caching, and serving of pre-generated TTS audio files
    for basic voice responses before implementing real-time streaming.
    """
    
    def __init__(self, audio_dir: str = "static/audio"):
        """
        Initialize the audio file manager.
        
        Args:
            audio_dir: Directory to store audio files
        """
        self.audio_dir = Path(audio_dir)
        self.audio_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (self.audio_dir / "tts").mkdir(exist_ok=True)
        (self.audio_dir / "temp").mkdir(exist_ok=True)
        
        logger.info(f"Audio file manager initialized with directory: {self.audio_dir}")
    
    def generate_audio_filename(self, text: str, voice_id: str = "default") -> str:
        """
        Generate a consistent filename for TTS audio based on text content.
        
        Args:
            text: Text content for TTS
            voice_id: Voice identifier
            
        Returns:
            Generated filename
        """
        # Create hash of text and voice for consistent naming
        content_hash = hashlib.md5(f"{text}_{voice_id}".encode()).hexdigest()[:12]
        return f"tts_{content_hash}.mp3"
    
    def get_audio_file_path(self, filename: str) -> Path:
        """
        Get the full path for an audio file.
        
        Args:
            filename: Audio filename
            
        Returns:
            Full path to audio file
        """
        return self.audio_dir / "tts" / filename
    
    def get_audio_url(self, filename: str, base_url: str = "") -> str:
        """
        Get the URL for serving an audio file.
        
        Args:
            filename: Audio filename
            base_url: Base URL for the application
            
        Returns:
            URL for accessing the audio file
        """
        return f"{base_url}/static/audio/tts/{filename}"
    
    def audio_file_exists(self, filename: str) -> bool:
        """
        Check if an audio file exists.
        
        Args:
            filename: Audio filename
            
        Returns:
            True if file exists, False otherwise
        """
        return self.get_audio_file_path(filename).exists()
    
    async def create_placeholder_audio_files(self) -> Dict[str, str]:
        """
        Create placeholder audio files for common responses.
        
        Returns:
            Dictionary mapping response types to filenames
        """
        placeholder_responses = {
            "greeting": "Hello! I'm your AI assistant. How can I help you today?",
            "error": "Sorry, there was an error processing your request. Please try again.",
            "goodbye": "Thank you for calling. Have a great day!",
            "connecting": "Please wait while I connect you to our AI assistant.",
            "unavailable": "I'm sorry, but voice services are currently unavailable. Please try again later.",
        }
        
        audio_files = {}
        
        for response_type, text in placeholder_responses.items():
            filename = f"{response_type}.mp3"
            file_path = self.get_audio_file_path(filename)
            
            if not file_path.exists():
                # Create a simple placeholder file (in a real implementation, 
                # this would use TTS service to generate actual audio)
                await self._create_placeholder_file(file_path, text)
            
            audio_files[response_type] = filename
        
        logger.info(f"Created {len(audio_files)} placeholder audio files")
        return audio_files
    
    async def _create_placeholder_file(self, file_path: Path, text: str) -> None:
        """
        Create a placeholder audio file.
        
        In a real implementation, this would use a TTS service to generate
        actual audio. For now, it creates an empty file as a placeholder.
        
        Args:
            file_path: Path where to create the file
            text: Text content for the audio
        """
        try:
            # Create an empty placeholder file
            # In production, this would call ElevenLabs or another TTS service
            file_path.write_text(f"# Placeholder audio file for: {text}\n")
            
            logger.debug(f"Created placeholder audio file: {file_path}")
            
        except Exception as e:
            logger.error(f"Error creating placeholder audio file {file_path}: {e}")
    
    async def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up temporary audio files older than specified age.
        
        Args:
            max_age_hours: Maximum age in hours for temp files
            
        Returns:
            Number of files cleaned up
        """
        temp_dir = self.audio_dir / "temp"
        if not temp_dir.exists():
            return 0
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        try:
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} temporary audio files")
            
        except Exception as e:
            logger.error(f"Error cleaning up temporary audio files: {e}")
        
        return cleaned_count


def convert_audio_format(
    input_data: bytes, 
    input_format: str, 
    output_format: str,
    sample_rate: Optional[int] = None
) -> bytes:
    """
    Convert audio data between different formats.
    
    This is a placeholder function. In a real implementation,
    you would use a library like pydub or ffmpeg for conversion.
    
    Args:
        input_data: Input audio data
        input_format: Input format (wav, mp3, etc.)
        output_format: Output format (wav, mp3, etc.)
        sample_rate: Target sample rate (optional)
        
    Returns:
        Converted audio data
    """
    logger.debug(
        f"Converting audio from {input_format} to {output_format}",
        input_size=len(input_data),
        sample_rate=sample_rate
    )
    
    # Placeholder implementation - in production, use actual audio conversion
    # For now, just return the input data
    return input_data


def validate_audio_format(format_name: str) -> bool:
    """
    Validate if an audio format is supported.
    
    Args:
        format_name: Audio format name
        
    Returns:
        True if supported, False otherwise
    """
    supported_formats = ["wav", "mp3", "mulaw", "linear16", "ogg", "flac"]
    return format_name.lower() in supported_formats


def get_audio_mime_type(format_name: str) -> str:
    """
    Get MIME type for an audio format.
    
    Args:
        format_name: Audio format name
        
    Returns:
        MIME type string
    """
    mime_types = {
        "wav": "audio/wav",
        "mp3": "audio/mpeg",
        "mulaw": "audio/basic",
        "linear16": "audio/l16",
        "ogg": "audio/ogg",
        "flac": "audio/flac",
    }
    
    return mime_types.get(format_name.lower(), "audio/octet-stream")


def calculate_audio_duration(
    data_size: int, 
    sample_rate: int, 
    channels: int = 1, 
    bits_per_sample: int = 16
) -> float:
    """
    Calculate audio duration from data size and format parameters.
    
    Args:
        data_size: Size of audio data in bytes
        sample_rate: Sample rate in Hz
        channels: Number of audio channels
        bits_per_sample: Bits per sample
        
    Returns:
        Duration in seconds
    """
    bytes_per_sample = bits_per_sample // 8
    bytes_per_second = sample_rate * channels * bytes_per_sample
    
    if bytes_per_second == 0:
        return 0.0
    
    return data_size / bytes_per_second


# Global audio file manager instance
audio_manager = AudioFileManager()
