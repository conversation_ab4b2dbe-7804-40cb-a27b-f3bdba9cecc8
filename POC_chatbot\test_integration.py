#!/usr/bin/env python3
"""
Quick integration test to verify <PERSON><PERSON><PERSON> and ElevenLabs STT services work.
Run this script to test the new service integrations.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_groq_service():
    """Test Groq service integration."""
    print("Testing Groq service...")
    
    try:
        from app.services.groq_service import create_groq_service
        
        # Create service
        groq_service = create_groq_service()
        
        # Initialize
        await groq_service.initialize()
        print("✓ Groq service initialized successfully")
        
        # Test health check
        health = await groq_service.health_check()
        print(f"✓ Groq health check: {health.status.value} - {health.message}")
        
        # Test simple response
        response = await groq_service.generate_simple_response(
            "Say hello in a friendly way",
            system_message="You are a helpful assistant."
        )
        print(f"✓ Groq response: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Groq service test failed: {e}")
        return False

async def test_elevenlabs_stt_service():
    """Test ElevenLabs STT service integration."""
    print("\nTesting ElevenLabs STT service...")
    
    try:
        from app.services.stt import create_stt_service
        
        # Create service
        stt_service = create_stt_service("elevenlabs")
        
        # Initialize
        await stt_service.initialize()
        print("✓ ElevenLabs STT service initialized successfully")
        
        # Test health check
        health = await stt_service.health_check()
        print(f"✓ ElevenLabs STT health check: {health.status.value} - {health.message}")
        
        return True
        
    except Exception as e:
        print(f"✗ ElevenLabs STT service test failed: {e}")
        return False

async def test_chatbot_integration():
    """Test chatbot with Groq integration."""
    print("\nTesting Chatbot with Groq integration...")
    
    try:
        from app.services.chatbot import ChatbotService
        
        # Create chatbot service
        chatbot = ChatbotService({})
        
        # Initialize
        await chatbot.initialize()
        print("✓ Chatbot service initialized successfully")
        
        # Test message processing
        response = await chatbot.process_message(
            session_id="test_session",
            message="Hello, how are you?",
            channel="sms"
        )
        print(f"✓ Chatbot response: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Chatbot integration test failed: {e}")
        return False

async def main():
    """Run all integration tests."""
    print("🚀 Starting integration tests...\n")
    
    # Check required environment variables
    required_vars = ["GROQ_API_KEY", "ELEVENLABS_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file and try again.")
        return
    
    # Run tests
    tests = [
        test_groq_service(),
        test_elevenlabs_stt_service(),
        test_chatbot_integration()
    ]
    
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    # Summary
    print("\n" + "="*50)
    print("INTEGRATION TEST SUMMARY")
    print("="*50)
    
    success_count = sum(1 for result in results if result is True)
    total_tests = len(results)
    
    if success_count == total_tests:
        print(f"🎉 All {total_tests} tests passed!")
        print("\nYour FastAPI chatbot is ready to use with:")
        print("- Groq LLM for intelligent responses")
        print("- ElevenLabs STT for speech recognition")
        print("- ElevenLabs TTS for speech synthesis")
    else:
        print(f"⚠️  {success_count}/{total_tests} tests passed")
        print("\nSome services may not be properly configured.")
        print("Check your .env file and API keys.")

if __name__ == "__main__":
    asyncio.run(main())
