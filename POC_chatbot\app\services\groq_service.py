"""
Groq LLM service for the FastAPI chatbot application.

This service provides language model capabilities using Groq's
high-performance inference API for generating chatbot responses.
"""

import time
from typing import Any, Dict, List, Optional
from groq import AsyncGroq

from app.services.base import BaseService, ServiceHealth, ServiceStatus
from app.config import settings, get_groq_config
from app.utils.logging import get_logger

logger = get_logger(__name__)


class GroqService(BaseService):
    """
    Groq Language Model service implementation.
    
    Provides high-performance language model inference using Groq's API
    for generating intelligent chatbot responses with fast inference times.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("groq", config)
        
        # Validate required configuration
        required_keys = ["api_key", "model"]
        self._validate_config(required_keys)
        
        # Initialize Groq client
        self.client: Optional[AsyncGroq] = None
        self.api_key = config["api_key"]
        self.model = config["model"]
        self.temperature = config.get("temperature", 0.7)
        self.max_tokens = config.get("max_tokens", 1024)
        
        # Supported models
        self.supported_models = [
            "llama3-8b-8192",
            "llama3-70b-8192", 
            "mixtral-8x7b-32768",
            "gemma-7b-it",
            "gemma2-9b-it"
        ]
    
    async def initialize(self) -> None:
        """Initialize the Groq service."""
        await super().initialize()
        
        try:
            # Initialize Groq async client
            self.client = AsyncGroq(api_key=self.api_key)
            
            # Test the connection by listing models
            models = await self.client.models.list()
            
            self.logger.info(
                "Groq service initialized successfully",
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                available_models=len(models.data) if hasattr(models, 'data') else 0
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Groq service: {e}")
            raise
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the Groq service."""
        if not self.client:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message="Groq client not initialized"
            )
        
        try:
            start_time = time.time()
            
            # Test API connectivity by listing models
            models = await self.client.models.list()
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="Groq service is operational",
                details={
                    "model": self.model,
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens,
                    "available_models": len(models.data) if hasattr(models, 'data') else 0,
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"Groq health check failed: {str(e)}"
            )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs: Any
    ) -> str:
        """
        Generate a response using Groq's language model.
        
        Args:
            messages: List of conversation messages
            system_message: Optional system message to set context
            temperature: Override default temperature
            max_tokens: Override default max tokens
            **kwargs: Additional parameters
            
        Returns:
            Generated response text
        """
        if not self.client:
            raise RuntimeError("Groq client not initialized")
        
        # Prepare messages for Groq API
        groq_messages = []
        
        # Add system message if provided
        if system_message:
            groq_messages.append({
                "role": "system",
                "content": system_message
            })
        
        # Add conversation messages
        for message in messages:
            groq_messages.append({
                "role": message.get("role", "user"),
                "content": message.get("content", "")
            })
        
        # Use provided parameters or defaults
        temp = temperature if temperature is not None else self.temperature
        max_tok = max_tokens if max_tokens is not None else self.max_tokens
        
        self.logger.info(
            "Generating response with Groq",
            model=self.model,
            message_count=len(groq_messages),
            temperature=temp,
            max_tokens=max_tok
        )
        
        try:
            start_time = time.time()
            
            # Call Groq API
            completion = await self.client.chat.completions.create(
                model=self.model,
                messages=groq_messages,
                temperature=temp,
                max_tokens=max_tok,
                top_p=kwargs.get("top_p", 1.0),
                stream=False
            )
            
            processing_time = time.time() - start_time
            
            # Extract response
            if completion.choices and len(completion.choices) > 0:
                response = completion.choices[0].message.content
                
                # Log usage statistics if available
                if hasattr(completion, 'usage'):
                    usage = completion.usage
                    self.logger.info(
                        "Groq response generated",
                        response_length=len(response),
                        processing_time_ms=round(processing_time * 1000, 2),
                        prompt_tokens=getattr(usage, 'prompt_tokens', 0),
                        completion_tokens=getattr(usage, 'completion_tokens', 0),
                        total_tokens=getattr(usage, 'total_tokens', 0)
                    )
                else:
                    self.logger.info(
                        "Groq response generated",
                        response_length=len(response),
                        processing_time_ms=round(processing_time * 1000, 2)
                    )
                
                return response.strip()
            else:
                raise ValueError("No response generated from Groq")
                
        except Exception as e:
            self.logger.error(
                "Error generating Groq response",
                error=str(e),
                model=self.model,
                message_count=len(groq_messages)
            )
            raise
    
    async def generate_simple_response(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        **kwargs: Any
    ) -> str:
        """
        Generate a simple response from a single prompt.
        
        Args:
            prompt: User prompt/question
            system_message: Optional system message
            **kwargs: Additional parameters
            
        Returns:
            Generated response text
        """
        messages = [{"role": "user", "content": prompt}]
        return await self.generate_response(
            messages=messages,
            system_message=system_message,
            **kwargs
        )
    
    async def generate_streaming_response(
        self,
        messages: List[Dict[str, str]],
        system_message: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs: Any
    ):
        """
        Generate a streaming response using Groq's language model.
        
        Args:
            messages: List of conversation messages
            system_message: Optional system message
            temperature: Override default temperature
            max_tokens: Override default max tokens
            **kwargs: Additional parameters
            
        Yields:
            Response chunks as they are generated
        """
        if not self.client:
            raise RuntimeError("Groq client not initialized")
        
        # Prepare messages for Groq API
        groq_messages = []
        
        if system_message:
            groq_messages.append({
                "role": "system",
                "content": system_message
            })
        
        for message in messages:
            groq_messages.append({
                "role": message.get("role", "user"),
                "content": message.get("content", "")
            })
        
        temp = temperature if temperature is not None else self.temperature
        max_tok = max_tokens if max_tokens is not None else self.max_tokens
        
        self.logger.info(
            "Starting streaming response with Groq",
            model=self.model,
            message_count=len(groq_messages)
        )
        
        try:
            # Call Groq API with streaming
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=groq_messages,
                temperature=temp,
                max_tokens=max_tok,
                top_p=kwargs.get("top_p", 1.0),
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta
                    if hasattr(delta, 'content') and delta.content:
                        yield delta.content
                        
        except Exception as e:
            self.logger.error(f"Error in streaming Groq response: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "supported_models": self.supported_models,
            "provider": "Groq"
        }
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """
        Get list of available models from Groq.
        
        Returns:
            List of available model information
        """
        if not self.client:
            raise RuntimeError("Groq client not initialized")
        
        try:
            models = await self.client.models.list()
            
            model_list = []
            if hasattr(models, 'data'):
                for model in models.data:
                    model_info = {
                        "id": model.id,
                        "object": getattr(model, 'object', 'model'),
                        "created": getattr(model, 'created', None),
                        "owned_by": getattr(model, 'owned_by', 'groq'),
                    }
                    model_list.append(model_info)
            
            return model_list
            
        except Exception as e:
            self.logger.error(f"Error fetching available models: {e}")
            return []


# Factory function to create Groq service
def create_groq_service() -> GroqService:
    """
    Create a Groq service instance with configuration.
    
    Returns:
        Groq service instance
    """
    config = get_groq_config()
    return GroqService(config)


# Global Groq service instance
groq_service = create_groq_service()
