"""
Web Chat API endpoints for the FastAPI Chatbot.
Provides REST endpoints for web-based chat interfaces.
"""

import uuid
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
import structlog

from app.services.chatbot import ChatbotService

# Configure logger
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/v1", tags=["chat"])

# Initialize chatbot service
chatbot_service = ChatbotService({})


class ChatMessage(BaseModel):
    """Chat message request model."""
    message: str = Field(..., min_length=1, max_length=1000, description="The user's message")
    session_id: str = Field(default=None, description="Optional session ID for conversation continuity")
    user_id: str = Field(default="web_user", description="User identifier")


class ChatResponse(BaseModel):
    """Chat response model."""
    response: str = Field(..., description="The chatbot's response")
    session_id: str = Field(..., description="Session ID for conversation continuity")
    message_id: str = Field(..., description="Unique message identifier")
    timestamp: str = Field(..., description="Response timestamp")


async def get_chatbot_service():
    """Dependency to get chatbot service instance."""
    if not chatbot_service.is_initialized():
        await chatbot_service.initialize()
    return chatbot_service


@router.post("/chat", response_model=ChatResponse)
async def web_chat(
    chat_message: ChatMessage,
    chatbot: Any = Depends(get_chatbot_service)
) -> ChatResponse:
    """
    Process a web chat message and return the chatbot's response.
    
    This endpoint provides a simple REST interface for web-based chat applications.
    Unlike the SMS webhook, this returns JSON instead of TwiML.
    """
    try:
        # Generate session ID if not provided
        if not chat_message.session_id:
            chat_message.session_id = f"web_{uuid.uuid4().hex[:12]}"
        
        # Log the incoming message
        logger.info(
            "Received web chat message",
            message_preview=chat_message.message[:50] + "..." if len(chat_message.message) > 50 else chat_message.message,
            session_id=chat_message.session_id,
            user_id=chat_message.user_id,
            message_length=len(chat_message.message)
        )
        
        # Process the message through the chatbot service
        response = await chatbot.process_message(
            session_id=chat_message.session_id,
            message=chat_message.message,
            channel="web",
            user_metadata={
                "user_id": chat_message.user_id,
                "source": "web_chat_api"
            }
        )
        
        # Generate unique message ID
        message_id = f"msg_{uuid.uuid4().hex[:8]}"
        
        # Get current timestamp
        from datetime import datetime
        timestamp = datetime.utcnow().isoformat() + "Z"
        
        # Log the response
        logger.info(
            "Generated web chat response",
            session_id=chat_message.session_id,
            response_length=len(response),
            message_id=message_id
        )
        
        return ChatResponse(
            response=response,
            session_id=chat_message.session_id,
            message_id=message_id,
            timestamp=timestamp
        )
        
    except Exception as e:
        logger.error(
            "Error processing web chat message",
            error=str(e),
            session_id=chat_message.session_id,
            message_preview=chat_message.message[:50]
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process chat message: {str(e)}"
        )


@router.get("/chat/health")
async def chat_health():
    """Health check endpoint for the chat API."""
    try:
        # Check if chatbot service is available
        if not chatbot_service.is_initialized():
            await chatbot_service.initialize()
        
        return {
            "status": "healthy",
            "service": "web_chat_api",
            "chatbot_initialized": chatbot_service.is_initialized()
        }
    except Exception as e:
        logger.error("Chat API health check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail=f"Chat API unhealthy: {str(e)}"
        )


@router.delete("/chat/session/{session_id}")
async def clear_chat_session(
    session_id: str,
    chatbot: Any = Depends(get_chatbot_service)
):
    """Clear a specific chat session."""
    try:
        # This would clear the session from the chatbot service
        # For now, we'll just log it since the current implementation
        # uses in-memory sessions that expire automatically
        logger.info("Chat session clear requested", session_id=session_id)
        
        return {
            "status": "success",
            "message": f"Session {session_id} cleared",
            "session_id": session_id
        }
        
    except Exception as e:
        logger.error("Error clearing chat session", error=str(e), session_id=session_id)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear session: {str(e)}"
        )
