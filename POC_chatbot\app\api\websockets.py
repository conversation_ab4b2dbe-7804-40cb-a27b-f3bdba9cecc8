"""
WebSocket endpoints for Twilio Media Streams and real-time audio processing.

This module contains WebSocket handlers for real-time audio streaming
between <PERSON><PERSON><PERSON> and the chatbot for voice conversations.
"""

import json
import asyncio
import base64
from typing import Any, Dict, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from websockets.exceptions import ConnectionClosed

from app.services.session import SessionService
from app.services.chatbot import ChatbotService
from app.services.stt import stt_service
from app.services.tts import tts_service
from app.services.groq_service import groq_service
from app.models.session import (
    ChannelType, 
    CallStatus, 
    MessageRole,
    VoiceCallSession
)
from app.models.requests import TwilioMediaStreamEvent
from app.utils.logging import get_logger
from app.config import settings

logger = get_logger(__name__)
router = APIRouter()

# Service instances
session_service = SessionService({})
chatbot_service = ChatbotService({})


async def get_session_service() -> SessionService:
    """Dependency to get session service instance."""
    if not session_service.is_initialized():
        await session_service.initialize()
    return session_service


async def get_chatbot_service() -> ChatbotService:
    """Dependency to get chatbot service instance."""
    if not chatbot_service.is_initialized():
        await chatbot_service.initialize()
    return chatbot_service


class MediaStreamHandler:
    """
    Handler for Twilio Media Streams WebSocket connections.
    
    Manages the lifecycle of media stream connections and handles
    real-time audio processing for voice conversations.
    """
    
    def __init__(
        self,
        websocket: WebSocket,
        session_service: SessionService,
        chatbot_service: ChatbotService
    ):
        self.websocket = websocket
        self.session_service = session_service
        self.chatbot_service = chatbot_service
        
        # Connection state
        self.stream_sid: Optional[str] = None
        self.call_sid: Optional[str] = None
        self.session_id: Optional[str] = None
        self.call_session: Optional[VoiceCallSession] = None
        
        # Audio processing state
        self.audio_buffer = bytearray()
        self.is_processing = False
        
        logger.info("Media stream handler initialized")
    
    async def handle_connection(self) -> None:
        """
        Handle the WebSocket connection lifecycle.
        
        Manages incoming messages from Twilio Media Streams and
        coordinates audio processing and response generation.
        """
        try:
            await self.websocket.accept()
            logger.info("Media stream WebSocket connection accepted")
            
            # Handle incoming messages
            async for message in self.websocket.iter_text():
                await self._process_message(message)
                
        except WebSocketDisconnect:
            logger.info("Media stream WebSocket disconnected")
            await self._cleanup_connection()
        except ConnectionClosed:
            logger.info("Media stream connection closed")
            await self._cleanup_connection()
        except Exception as e:
            logger.error(f"Error in media stream handler: {e}", exc_info=True)
            await self._cleanup_connection()
    
    async def _process_message(self, message: str) -> None:
        """
        Process incoming WebSocket message from Twilio.
        
        Args:
            message: JSON message from Twilio Media Streams
        """
        try:
            data = json.loads(message)
            event_type = data.get("event")
            
            logger.debug(f"Received media stream event: {event_type}")
            
            if event_type == "connected":
                await self._handle_connected(data)
            elif event_type == "start":
                await self._handle_start(data)
            elif event_type == "media":
                await self._handle_media(data)
            elif event_type == "stop":
                await self._handle_stop(data)
            else:
                logger.warning(f"Unknown media stream event: {event_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in media stream message: {e}")
        except Exception as e:
            logger.error(f"Error processing media stream message: {e}")
    
    async def _handle_connected(self, data: Dict[str, Any]) -> None:
        """Handle WebSocket connected event."""
        logger.info("Media stream connected", protocol=data.get("protocol"))
    
    async def _handle_start(self, data: Dict[str, Any]) -> None:
        """
        Handle media stream start event.
        
        Args:
            data: Start event data from Twilio
        """
        start_data = data.get("start", {})
        self.stream_sid = start_data.get("streamSid")
        self.call_sid = start_data.get("callSid")
        
        logger.info(
            "Media stream started",
            stream_sid=self.stream_sid,
            call_sid=self.call_sid,
            account_sid=start_data.get("accountSid")
        )
        
        # Create or get session for this call
        if self.call_sid:
            await self._initialize_call_session(start_data)
        
        # Send initial greeting or instructions
        await self._send_initial_response()
    
    async def _handle_media(self, data: Dict[str, Any]) -> None:
        """
        Handle incoming audio media data.
        
        Args:
            data: Media event data containing audio payload
        """
        if self.is_processing:
            return  # Skip if already processing audio
        
        media_data = data.get("media", {})
        payload = media_data.get("payload")
        
        if not payload:
            return
        
        try:
            # Decode base64 audio data
            audio_data = base64.b64decode(payload)
            self.audio_buffer.extend(audio_data)
            
            # Process audio when buffer reaches threshold
            if len(self.audio_buffer) >= 8000:  # ~1 second of audio at 8kHz
                await self._process_audio_buffer()
                
        except Exception as e:
            logger.error(f"Error handling media data: {e}")
    
    async def _handle_stop(self, data: Dict[str, Any]) -> None:
        """
        Handle media stream stop event.
        
        Args:
            data: Stop event data from Twilio
        """
        logger.info("Media stream stopped", stream_sid=self.stream_sid)
        
        # Update call session status
        if self.call_session:
            await self.session_service.update_call_status(
                self.call_sid,
                CallStatus.COMPLETED
            )
        
        await self._cleanup_connection()
    
    async def _initialize_call_session(self, start_data: Dict[str, Any]) -> None:
        """
        Initialize call session from start data.
        
        Args:
            start_data: Start event data from Twilio
        """
        try:
            # Extract call information
            custom_parameters = start_data.get("customParameters", {})
            from_number = custom_parameters.get("From", "unknown")
            to_number = custom_parameters.get("To", "unknown")
            
            # Generate session ID
            self.session_id = f"voice_{self.call_sid}"
            
            # Create conversation session
            await self.session_service.create_session(
                session_id=self.session_id,
                channel=ChannelType.VOICE,
                user_phone=from_number,
                bot_phone=to_number
            )
            
            # Create call session
            self.call_session = await self.session_service.create_voice_call_session(
                call_sid=self.call_sid,
                session_id=self.session_id,
                from_number=from_number,
                to_number=to_number,
                direction="inbound",
                status=CallStatus.IN_PROGRESS,
                stream_sid=self.stream_sid,
                streaming_enabled=True
            )
            
            logger.info(
                "Call session initialized",
                session_id=self.session_id,
                call_sid=self.call_sid,
                from_number=from_number
            )
            
        except Exception as e:
            logger.error(f"Error initializing call session: {e}")
    
    async def _send_initial_response(self) -> None:
        """Send initial greeting to the caller."""
        try:
            greeting = "Hello! I'm your AI assistant. I can hear you now. Please go ahead and speak."
            
            # Add message to session
            if self.session_id:
                await self.session_service.add_message_to_session(
                    session_id=self.session_id,
                    content=greeting,
                    role=MessageRole.ASSISTANT,
                    channel=ChannelType.VOICE
                )
            
            # Convert to audio and send (placeholder implementation)
            await self._send_audio_response(greeting)
            
        except Exception as e:
            logger.error(f"Error sending initial response: {e}")
    
    async def _process_audio_buffer(self) -> None:
        """
        Process accumulated audio data for speech recognition.
        
        This is a placeholder implementation. In production, this would:
        1. Send audio to STT service (OpenAI Whisper, etc.)
        2. Process transcribed text through chatbot
        3. Generate TTS response
        4. Stream audio back to caller
        """
        if self.is_processing:
            return
        
        self.is_processing = True
        
        try:
            # Placeholder: simulate STT processing
            audio_data = bytes(self.audio_buffer)
            self.audio_buffer.clear()
            
            logger.debug(f"Processing audio buffer of {len(audio_data)} bytes")
            
            # Process audio through STT service
            transcribed_text = await self._process_stt(audio_data)
            
            if transcribed_text and self.session_id:
                # Add user message to session
                await self.session_service.add_message_to_session(
                    session_id=self.session_id,
                    content=transcribed_text,
                    role=MessageRole.USER,
                    channel=ChannelType.VOICE
                )
                
                # Process through chatbot
                response = await self.chatbot_service.process_message(
                    session_id=self.session_id,
                    message=transcribed_text,
                    channel="voice"
                )
                
                # Send audio response
                await self._send_audio_response(response)
                
        except Exception as e:
            logger.error(f"Error processing audio buffer: {e}")
        finally:
            self.is_processing = False
    
    async def _process_stt(self, audio_data: bytes) -> Optional[str]:
        """
        Process speech-to-text using the STT service.

        Args:
            audio_data: Raw audio data from Twilio (mulaw format)

        Returns:
            Transcribed text or None
        """
        try:
            # Only process substantial audio
            if len(audio_data) < 1000:
                return None

            # Initialize STT service if needed
            if not stt_service.is_initialized():
                await stt_service.initialize()

            # Transcribe audio using OpenAI Whisper
            text = await stt_service.transcribe_twilio_audio(audio_data)

            if text and len(text.strip()) > 0:
                self.logger.info(f"STT transcription: {text[:100]}...")
                return text.strip()

            return None

        except Exception as e:
            self.logger.error(f"STT processing error: {e}")
            return None
    
    async def _send_audio_response(self, text: str) -> None:
        """
        Convert text to audio and send to caller.
        
        This is a placeholder implementation. In production, this would:
        1. Use TTS service (ElevenLabs) to generate audio
        2. Stream audio data back through WebSocket
        
        Args:
            text: Text to convert to speech
        """
        try:
            logger.info(f"Sending audio response: {text[:50]}...")
            
            # Process text through TTS service
            audio_data = await self._process_tts(text)
            
            if audio_data:
                # Send audio data to Twilio
                await self._send_media_message(audio_data)
                
        except Exception as e:
            logger.error(f"Error sending audio response: {e}")
    
    async def _process_tts(self, text: str) -> Optional[bytes]:
        """
        Process text-to-speech using the TTS service.

        Args:
            text: Text to convert to speech

        Returns:
            Audio data as bytes (mulaw format for Twilio)
        """
        try:
            # Initialize TTS service if needed
            if not tts_service.is_initialized():
                await tts_service.initialize()

            # Generate speech using ElevenLabs
            audio_data = await tts_service.synthesize_speech(text)

            # Convert to mulaw format for Twilio if needed
            # (In production, you might need format conversion)
            self.logger.info(f"TTS generated {len(audio_data)} bytes for: {text[:50]}...")

            return audio_data

        except Exception as e:
            self.logger.error(f"TTS processing error: {e}")
            return None
    
    async def _send_media_message(self, audio_data: bytes) -> None:
        """
        Send audio data to Twilio via WebSocket.
        
        Args:
            audio_data: Audio data to send
        """
        try:
            # Encode audio data as base64
            payload = base64.b64encode(audio_data).decode('utf-8')
            
            # Create media message
            message = {
                "event": "media",
                "streamSid": self.stream_sid,
                "media": {
                    "payload": payload
                }
            }
            
            # Send to Twilio
            await self.websocket.send_text(json.dumps(message))
            
        except Exception as e:
            logger.error(f"Error sending media message: {e}")
    
    async def _cleanup_connection(self) -> None:
        """Clean up connection resources."""
        try:
            # Update call session if exists
            if self.call_session and self.call_sid:
                await self.session_service.update_call_status(
                    self.call_sid,
                    CallStatus.COMPLETED
                )
            
            logger.info("Media stream connection cleaned up")
            
        except Exception as e:
            logger.error(f"Error during connection cleanup: {e}")


@router.websocket("/media-stream")
async def media_stream_endpoint(
    websocket: WebSocket,
    session_svc: SessionService = Depends(get_session_service),
    chatbot_svc: ChatbotService = Depends(get_chatbot_service)
):
    """
    WebSocket endpoint for Twilio Media Streams.
    
    Handles real-time audio streaming for voice conversations
    between callers and the AI assistant.
    """
    logger.info("New media stream connection")
    
    # Create handler and process connection
    handler = MediaStreamHandler(websocket, session_svc, chatbot_svc)
    await handler.handle_connection()
