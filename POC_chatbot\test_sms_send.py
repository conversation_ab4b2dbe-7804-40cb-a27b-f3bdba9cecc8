#!/usr/bin/env python3
"""
Test script to verify SMS sending functionality
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_sms_send():
    """Test sending SMS via Twilio"""
    try:
        from twilio.rest import Client
        
        # Get Twilio credentials
        account_sid = os.getenv("TWILIO_ACCOUNT_SID")
        auth_token = os.getenv("TWILIO_AUTH_TOKEN")
        twilio_number = os.getenv("TWILIO_PHONE_NUMBER")
        
        print(f"Account SID: {account_sid}")
        print(f"Twilio Number: {twilio_number}")
        print(f"Auth Token: {'*' * len(auth_token) if auth_token else 'None'}")
        
        if not all([account_sid, auth_token, twilio_number]):
            print("❌ Missing Twilio credentials in .env file")
            return False
        
        # Initialize Twilio client
        client = Client(account_sid, auth_token)
        
        # Test message
        test_message = "Hello! This is a test message from your AI chatbot via Streamlit interface."
        test_to_number = "+**********"  # Replace with your actual phone number for testing
        
        print(f"\n📱 Attempting to send SMS...")
        print(f"From: {twilio_number}")
        print(f"To: {test_to_number}")
        print(f"Message: {test_message}")
        
        # Send SMS (commented out to avoid accidental sends during testing)
        # message = client.messages.create(
        #     body=test_message,
        #     from_=twilio_number,
        #     to=test_to_number
        # )
        # 
        # print(f"✅ SMS sent successfully!")
        # print(f"Message SID: {message.sid}")
        # print(f"Status: {message.status}")
        
        print("✅ Twilio client initialized successfully!")
        print("📝 Note: Actual SMS sending is commented out to prevent accidental sends")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing SMS sending functionality...")
    success = test_sms_send()
    if success:
        print("\n🎉 SMS functionality test passed!")
    else:
        print("\n💥 SMS functionality test failed!")
