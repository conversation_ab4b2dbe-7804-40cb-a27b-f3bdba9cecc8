"""
Base service classes and abstractions for external API integrations.

This module provides base classes and interfaces that all service
implementations should follow for consistency and testability.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncIterator
from dataclasses import dataclass
from enum import Enum

from app.utils.logging import LoggerMixin


class ServiceStatus(Enum):
    """Service status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class ServiceHealth:
    """Service health information."""
    status: ServiceStatus
    message: str
    details: Optional[Dict[str, Any]] = None
    response_time_ms: Optional[float] = None


class BaseService(ABC, LoggerMixin):
    """
    Base class for all external service integrations.
    
    Provides common functionality like health checks, error handling,
    and logging that all services should implement.
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Initialize the base service.
        
        Args:
            name: Service name for logging and identification
            config: Service configuration dictionary
        """
        self.name = name
        self.config = config
        self._initialized = False
    
    async def initialize(self) -> None:
        """
        Initialize the service.
        
        Override this method to perform any async initialization
        required by the service (e.g., establishing connections).
        """
        self.logger.info(f"Initializing {self.name} service")
        self._initialized = True
    
    async def cleanup(self) -> None:
        """
        Cleanup service resources.
        
        Override this method to perform cleanup when the service
        is being shut down (e.g., closing connections).
        """
        self.logger.info(f"Cleaning up {self.name} service")
        self._initialized = False
    
    @abstractmethod
    async def health_check(self) -> ServiceHealth:
        """
        Check the health of the service.
        
        Returns:
            ServiceHealth object with current status
        """
        pass
    
    def is_initialized(self) -> bool:
        """Check if the service is initialized."""
        return self._initialized
    
    def _validate_config(self, required_keys: List[str]) -> None:
        """
        Validate that required configuration keys are present.
        
        Args:
            required_keys: List of required configuration keys
            
        Raises:
            ValueError: If any required keys are missing
        """
        missing_keys = [key for key in required_keys if key not in self.config]
        if missing_keys:
            raise ValueError(f"Missing required config keys for {self.name}: {missing_keys}")


class BaseMessagingService(BaseService):
    """
    Base class for messaging services (SMS, etc.).
    """
    
    @abstractmethod
    async def send_message(
        self, 
        to: str, 
        message: str, 
        from_number: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Send a message.
        
        Args:
            to: Recipient phone number
            message: Message content
            from_number: Sender phone number (optional)
            **kwargs: Additional service-specific parameters
            
        Returns:
            Dictionary with send result information
        """
        pass


class BaseTTSService(BaseService):
    """
    Base class for Text-to-Speech services.
    """
    
    @abstractmethod
    async def synthesize_speech(
        self, 
        text: str, 
        voice_id: Optional[str] = None,
        **kwargs: Any
    ) -> bytes:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice identifier (optional)
            **kwargs: Additional service-specific parameters
            
        Returns:
            Audio data as bytes
        """
        pass
    
    @abstractmethod
    async def stream_speech(
        self, 
        text: str, 
        voice_id: Optional[str] = None,
        **kwargs: Any
    ) -> AsyncIterator[bytes]:
        """
        Stream speech synthesis in real-time.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice identifier (optional)
            **kwargs: Additional service-specific parameters
            
        Yields:
            Audio data chunks as bytes
        """
        pass


class BaseSTTService(BaseService):
    """
    Base class for Speech-to-Text services.
    """
    
    @abstractmethod
    async def transcribe_audio(
        self, 
        audio_data: bytes, 
        format: str = "wav",
        language: Optional[str] = None,
        **kwargs: Any
    ) -> str:
        """
        Transcribe audio to text.
        
        Args:
            audio_data: Audio data as bytes
            format: Audio format (wav, mp3, etc.)
            language: Language code (optional)
            **kwargs: Additional service-specific parameters
            
        Returns:
            Transcribed text
        """
        pass
    
    @abstractmethod
    async def stream_transcription(
        self, 
        audio_stream: AsyncIterator[bytes],
        format: str = "wav",
        language: Optional[str] = None,
        **kwargs: Any
    ) -> AsyncIterator[str]:
        """
        Stream audio transcription in real-time.
        
        Args:
            audio_stream: Stream of audio data chunks
            format: Audio format
            language: Language code (optional)
            **kwargs: Additional service-specific parameters
            
        Yields:
            Transcribed text chunks
        """
        pass


class BaseVoiceService(BaseService):
    """
    Base class for voice call services.
    """
    
    @abstractmethod
    async def make_call(
        self, 
        to: str, 
        from_number: str,
        webhook_url: str,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Make an outbound voice call.
        
        Args:
            to: Recipient phone number
            from_number: Caller phone number
            webhook_url: URL for call webhooks
            **kwargs: Additional service-specific parameters
            
        Returns:
            Dictionary with call information
        """
        pass
    
    @abstractmethod
    def generate_twiml_response(
        self, 
        message: Optional[str] = None,
        actions: Optional[List[Dict[str, Any]]] = None,
        **kwargs: Any
    ) -> str:
        """
        Generate TwiML response for voice calls.
        
        Args:
            message: Message to speak (optional)
            actions: List of TwiML actions (optional)
            **kwargs: Additional TwiML parameters
            
        Returns:
            TwiML XML string
        """
        pass


class ServiceRegistry:
    """
    Registry for managing service instances.
    
    Provides a centralized way to register, retrieve, and manage
    service instances throughout the application.
    """
    
    def __init__(self):
        self._services: Dict[str, BaseService] = {}
    
    def register(self, service: BaseService) -> None:
        """
        Register a service instance.
        
        Args:
            service: Service instance to register
        """
        self._services[service.name] = service
    
    def get(self, name: str) -> Optional[BaseService]:
        """
        Get a service by name.
        
        Args:
            name: Service name
            
        Returns:
            Service instance or None if not found
        """
        return self._services.get(name)
    
    def get_all(self) -> Dict[str, BaseService]:
        """Get all registered services."""
        return self._services.copy()
    
    async def initialize_all(self) -> None:
        """Initialize all registered services."""
        for service in self._services.values():
            await service.initialize()
    
    async def cleanup_all(self) -> None:
        """Cleanup all registered services."""
        for service in self._services.values():
            await service.cleanup()
    
    async def health_check_all(self) -> Dict[str, ServiceHealth]:
        """
        Perform health checks on all services.
        
        Returns:
            Dictionary mapping service names to health status
        """
        health_results = {}
        for name, service in self._services.items():
            try:
                health_results[name] = await service.health_check()
            except Exception as e:
                health_results[name] = ServiceHealth(
                    status=ServiceStatus.UNHEALTHY,
                    message=f"Health check failed: {str(e)}"
                )
        return health_results


# Global service registry instance
service_registry = ServiceRegistry()
