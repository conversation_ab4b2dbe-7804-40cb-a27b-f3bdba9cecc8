#!/usr/bin/env python3
"""
Simple test to check if basic imports work.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("Testing basic imports...")

try:
    from app.config import settings
    print("✓ Config imported successfully")
    print(f"✓ Groq API key configured: {'Yes' if settings.groq_api_key else 'No'}")
    print(f"✓ ElevenLabs API key configured: {'Yes' if settings.elevenlabs_api_key else 'No'}")
except Exception as e:
    print(f"✗ Config import failed: {e}")

try:
    import groq
    print("✓ Groq library imported successfully")
except Exception as e:
    print(f"✗ Groq import failed: {e}")

try:
    import elevenlabs
    print("✓ ElevenLabs library imported successfully")
except Exception as e:
    print(f"✗ ElevenLabs import failed: {e}")

print("\nDone!")
