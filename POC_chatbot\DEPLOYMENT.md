# FastAPI Chatbot Deployment Guide

This guide provides comprehensive instructions for deploying the FastAPI Chatbot application in various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Local Development](#local-development)
4. [Docker Deployment](#docker-deployment)
5. [Production Deployment](#production-deployment)
6. [Cloud Deployment](#cloud-deployment)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Python**: 3.11 or higher
- **Docker**: 20.10 or higher (for containerized deployment)
- **Docker Compose**: 2.0 or higher
- **Memory**: Minimum 2GB RAM (4GB+ recommended for production)
- **Storage**: Minimum 10GB free space

### External Services

Before deployment, ensure you have accounts and API keys for:

1. **Twilio** (for SMS and Voice)
   - Account SID
   - Auth Token
   - Phone Number

2. **ElevenLabs** (for Text-to-Speech)
   - API Key
   - Voice ID

3. **OpenAI** (for Speech-to-Text)
   - API Key

## Environment Configuration

### 1. Create Environment File

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

### 2. Configure Required Variables

Edit `.env` file with your specific values:

```bash
# Server Configuration
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+**********
TWILIO_VALIDATE_WEBHOOKS=true

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_VOICE_ID=your_voice_id_here
ELEVENLABS_MODEL_ID=eleven_monolingual_v1

# OpenAI Configuration
OPENAI_API_KEY=your_api_key_here
OPENAI_STT_MODEL=whisper-1

# Database Configuration (for production)
DATABASE_URL=postgresql://user:password@localhost:5432/chatbot_db
REDIS_URL=redis://localhost:6379/0

# Feature Flags
ENABLE_SMS=true
ENABLE_VOICE=true
ENABLE_VOICE_STREAMING=true
ENABLE_WEBHOOKS=true
```

## Local Development

### 1. Install Dependencies

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Run the Application

```bash
# Start the development server
python -m app.main

# Or using uvicorn directly
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. Test the Application

```bash
# Health check
curl http://localhost:8000/health

# API documentation
open http://localhost:8000/docs
```

## Docker Deployment

### 1. Build and Run with Docker Compose

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f chatbot-api

# Stop services
docker-compose down
```

### 2. Production Docker Compose

For production with monitoring:

```bash
# Start with production profile
docker-compose --profile production up -d

# Start with monitoring
docker-compose --profile monitoring up -d

# Start everything
docker-compose --profile production --profile monitoring up -d
```

### 3. Individual Docker Commands

```bash
# Build the image
docker build -t fastapi-chatbot .

# Run the container
docker run -d \
  --name chatbot-api \
  -p 8000:8000 \
  --env-file .env \
  fastapi-chatbot
```

## Production Deployment

### 1. Server Setup

#### Ubuntu/Debian Server

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y nginx certbot python3-certbot-nginx
```

### 2. SSL Certificate Setup

```bash
# Install SSL certificate with Let's Encrypt
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Nginx Configuration

Create `/etc/nginx/sites-available/chatbot`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /media-stream {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/chatbot /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. Systemd Service (Alternative to Docker)

Create `/etc/systemd/system/chatbot.service`:

```ini
[Unit]
Description=FastAPI Chatbot
After=network.target

[Service]
Type=simple
User=chatbot
WorkingDirectory=/opt/chatbot
Environment=PATH=/opt/chatbot/venv/bin
ExecStart=/opt/chatbot/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:

```bash
sudo systemctl enable chatbot
sudo systemctl start chatbot
sudo systemctl status chatbot
```

## Cloud Deployment

### AWS ECS/Fargate

1. **Build and push to ECR**:

```bash
# Create ECR repository
aws ecr create-repository --repository-name fastapi-chatbot

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin **********12.dkr.ecr.us-east-1.amazonaws.com

# Build and tag
docker build -t fastapi-chatbot .
docker tag fastapi-chatbot:latest **********12.dkr.ecr.us-east-1.amazonaws.com/fastapi-chatbot:latest

# Push
docker push **********12.dkr.ecr.us-east-1.amazonaws.com/fastapi-chatbot:latest
```

2. **Create ECS task definition** (see `aws/task-definition.json`)

3. **Deploy with ECS service**

### Azure Container Instances

```bash
# Create resource group
az group create --name chatbot-rg --location eastus

# Create container instance
az container create \
  --resource-group chatbot-rg \
  --name chatbot-api \
  --image your-registry/fastapi-chatbot:latest \
  --dns-name-label chatbot-api \
  --ports 8000 \
  --environment-variables \
    ENVIRONMENT=production \
    TWILIO_ACCOUNT_SID=your_sid \
    TWILIO_AUTH_TOKEN=your_token
```

### Google Cloud Run

```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/your-project/fastapi-chatbot

# Deploy to Cloud Run
gcloud run deploy chatbot-api \
  --image gcr.io/your-project/fastapi-chatbot \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENVIRONMENT=production
```

## Monitoring and Logging

### 1. Application Logs

```bash
# Docker logs
docker-compose logs -f chatbot-api

# File logs (if configured)
tail -f logs/app.log
```

### 2. Health Checks

```bash
# Basic health check
curl https://your-domain.com/health

# Detailed readiness check
curl https://your-domain.com/ready

# Metrics endpoint (if enabled)
curl https://your-domain.com/metrics
```

### 3. Monitoring Setup

Access monitoring dashboards:

- **Grafana**: http://your-domain.com:3000 (admin/admin)
- **Prometheus**: http://your-domain.com:9090

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   sudo lsof -i :8000
   sudo kill -9 <PID>
   ```

2. **Permission denied**:
   ```bash
   sudo chown -R $USER:$USER /path/to/app
   chmod +x scripts/*.sh
   ```

3. **Database connection issues**:
   ```bash
   # Check database status
   docker-compose ps postgres
   docker-compose logs postgres
   ```

4. **SSL certificate issues**:
   ```bash
   sudo certbot renew --dry-run
   sudo nginx -t
   ```

### Log Analysis

```bash
# Check application logs
docker-compose logs chatbot-api | grep ERROR

# Check system resources
docker stats

# Check disk usage
df -h
du -sh logs/
```

### Performance Tuning

1. **Increase worker processes**:
   ```bash
   uvicorn app.main:app --workers 4 --host 0.0.0.0 --port 8000
   ```

2. **Optimize database connections**:
   - Configure connection pooling
   - Set appropriate timeout values

3. **Enable caching**:
   - Configure Redis for session storage
   - Implement response caching

### Support

For additional support:

1. Check the application logs
2. Review the health check endpoints
3. Consult the API documentation at `/docs`
4. Check external service status (Twilio, ElevenLabs, OpenAI)

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **API Keys**: Rotate API keys regularly
3. **HTTPS**: Always use HTTPS in production
4. **Firewall**: Configure appropriate firewall rules
5. **Updates**: Keep dependencies and system packages updated
6. **Monitoring**: Set up alerts for security events
