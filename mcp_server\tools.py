"""
MCP tools implementation for external server.

This module implements the three main MCP tools that communicate
with SmartHR via HTTP API:
- extract_top_candidates
- generate_interview_questions
- evaluate_interview
"""

import logging
import time
from typing import List, Dict, Any
from models import (
    ExtractCandidatesRequest, ExtractCandidatesResponse,
    GenerateQuestionsRequest, GenerateQuestionsResponse,
    EvaluateInterviewRequest, EvaluateInterviewResponse,
    CandidateMatch, InterviewQuestion, InterviewEvaluation,
    SeniorityLevel, SkillCategory, ExpectedAnswer, QuestionEvaluation
)
from smarthr_client import get_smarthr_client
from config import get_mcp_config

logger = logging.getLogger(__name__)


async def extract_top_candidates(request: ExtractCandidatesRequest) -> ExtractCandidatesResponse:
    """
    Extract and match top candidates for a job position.
    
    This tool uses SmartHR's vectorization and search capabilities to find
    candidates that best match the provided job position requirements.
    """
    start_time = time.time()
    
    try:
        config = get_mcp_config()
        
        if not config.enable_extract_candidates:
            return ExtractCandidatesResponse(
                success=False,
                candidates=[],
                total_candidates_found=0,
                error_message="Extract candidates tool is disabled"
            )
        
        logger.info(f"Extracting candidates for position: {request.position.title}")
        
        # Get SmartHR client
        smarthr_client = get_smarthr_client()
        
        # Get candidates from SmartHR
        candidates_data = await smarthr_client.get_candidates(
            request.position, 
            request.max_candidates
        )
        
        # Convert to CandidateMatch objects
        candidate_matches = []
        for candidate_data in candidates_data:
            try:
                candidate_match = CandidateMatch(
                    candidate_id=candidate_data.get("id", ""),
                    name=candidate_data.get("name", ""),
                    email=candidate_data.get("email"),
                    similarity_score=candidate_data.get("similarity_score", 0.0),
                    skill_match_score=candidate_data.get("skill_match_score", 0.0),
                    experience_match_score=candidate_data.get("experience_match_score", 0.0),
                    seniority_match=SeniorityLevel(candidate_data.get("seniority_level", "mid")),
                    matching_skills=candidate_data.get("matching_skills", []),
                    missing_skills=candidate_data.get("missing_skills", []),
                    years_of_experience=candidate_data.get("years_of_experience"),
                    summary=candidate_data.get("summary")
                )
                
                # Filter by similarity score
                if candidate_match.similarity_score >= request.min_similarity_score:
                    candidate_matches.append(candidate_match)
                    
            except Exception as e:
                logger.warning(f"Error processing candidate data: {e}")
                continue
        
        # Filter by seniority if requested
        if request.filter_by_seniority:
            candidate_matches = [
                c for c in candidate_matches 
                if c.seniority_match == request.position.seniority_level
            ]
        
        # Sort by similarity score
        candidate_matches.sort(key=lambda x: x.similarity_score, reverse=True)
        
        # Limit results
        candidate_matches = candidate_matches[:request.max_candidates]
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"Successfully extracted {len(candidate_matches)} candidates")
        
        return ExtractCandidatesResponse(
            success=True,
            candidates=candidate_matches,
            total_candidates_found=len(candidates_data),
            search_metadata={
                "position_id": request.position.position_id,
                "position_title": request.position.title,
                "similarity_threshold": request.min_similarity_score,
                "seniority_filter": request.filter_by_seniority,
                "skills_analysis": request.include_skills_analysis
            },
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        logger.error(f"Error in extract_top_candidates: {e}")
        
        return ExtractCandidatesResponse(
            success=False,
            candidates=[],
            total_candidates_found=0,
            error_message=str(e),
            processing_time_ms=processing_time
        )


async def generate_interview_questions(request: GenerateQuestionsRequest) -> GenerateQuestionsResponse:
    """
    Generate interview questions with expected responses.
    
    This tool creates interview questions tailored to the position
    with expected answers for different seniority levels.
    """
    start_time = time.time()
    
    try:
        config = get_mcp_config()
        
        if not config.enable_generate_questions:
            return GenerateQuestionsResponse(
                success=False,
                questions=[],
                error_message="Generate questions tool is disabled"
            )
        
        logger.info(f"Generating questions for position: {request.position_id}")
        
        # Get SmartHR client
        smarthr_client = get_smarthr_client()
        
        # Get position information
        position_info = await smarthr_client.get_position_info(request.position_id)
        
        # Generate questions via SmartHR
        questions_data = await smarthr_client.generate_questions(
            request.position_id,
            request.num_questions,
            request.target_seniority
        )
        
        # Convert to InterviewQuestion objects
        interview_questions = []
        for i, question_data in enumerate(questions_data, 1):
            try:
                # Create expected answers
                expected_answers = ExpectedAnswer(
                    junior_answer=question_data.get("junior_answer", "Basic understanding expected"),
                    mid_answer=question_data.get("mid_answer", "Good understanding with examples"),
                    senior_answer=question_data.get("senior_answer", "Deep understanding with leadership aspects")
                )
                
                interview_question = InterviewQuestion(
                    question_number=i,
                    question_text=question_data.get("question", ""),
                    category=SkillCategory(question_data.get("category", "Technical Skills")),
                    expected_answers=expected_answers,
                    difficulty_level=SeniorityLevel(question_data.get("difficulty", "mid")),
                    evaluation_criteria=question_data.get("evaluation_criteria", [])
                )
                
                interview_questions.append(interview_question)
                
            except Exception as e:
                logger.warning(f"Error processing question data: {e}")
                continue
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"Successfully generated {len(interview_questions)} questions")
        
        return GenerateQuestionsResponse(
            success=True,
            questions=interview_questions,
            position_info=position_info,
            generation_metadata={
                "position_id": request.position_id,
                "target_seniority": request.target_seniority.value,
                "requested_questions": request.num_questions,
                "generated_questions": len(interview_questions),
                "categories": [cat.value for cat in request.categories] if request.categories else "all"
            },
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        logger.error(f"Error in generate_interview_questions: {e}")
        
        return GenerateQuestionsResponse(
            success=False,
            questions=[],
            error_message=str(e),
            processing_time_ms=processing_time
        )


async def evaluate_interview(request: EvaluateInterviewRequest) -> EvaluateInterviewResponse:
    """
    Evaluate interview transcripts using the four-agent system.
    
    This tool processes interview transcripts and provides comprehensive
    evaluation using SmartHR's existing four-agent evaluation system.
    """
    start_time = time.time()
    
    try:
        config = get_mcp_config()
        
        if not config.enable_evaluate_interview:
            return EvaluateInterviewResponse(
                success=False,
                evaluation=None,
                error_message="Evaluate interview tool is disabled"
            )
        
        logger.info(f"Evaluating interview: {request.interview_id}")
        
        # Get SmartHR client
        smarthr_client = get_smarthr_client()
        
        # Prepare interview data for evaluation
        interview_data = {
            "interview_id": request.interview_id,
            "transcript": request.transcript,
            "questions": request.questions,
            "position_id": request.position_id,
            "candidate_id": request.candidate_id,
            "use_four_agent_system": request.use_four_agent_system,
            "include_detailed_analysis": request.include_detailed_analysis
        }
        
        # Evaluate via SmartHR
        evaluation_data = await smarthr_client.evaluate_interview(interview_data)
        
        if not evaluation_data:
            raise Exception("No evaluation data received from SmartHR")
        
        # Convert to InterviewEvaluation object
        question_evaluations = []
        for q_eval_data in evaluation_data.get("question_evaluations", []):
            question_eval = QuestionEvaluation(
                question_number=q_eval_data.get("question_number", 0),
                question_text=q_eval_data.get("question_text", ""),
                candidate_answer=q_eval_data.get("candidate_answer", ""),
                detected_seniority=SeniorityLevel(q_eval_data.get("detected_seniority", "mid")),
                confidence_score=q_eval_data.get("confidence_score", 0.0),
                is_valid_response=q_eval_data.get("is_valid_response", False),
                similarity_scores=q_eval_data.get("similarity_scores", {}),
                evaluation_reasoning=q_eval_data.get("evaluation_reasoning", "")
            )
            question_evaluations.append(question_eval)
        
        interview_evaluation = InterviewEvaluation(
            overall_seniority=SeniorityLevel(evaluation_data.get("overall_seniority", "mid")),
            confidence_score=evaluation_data.get("confidence_score", 0.0),
            question_evaluations=question_evaluations,
            total_questions=evaluation_data.get("total_questions", 0),
            valid_responses=evaluation_data.get("valid_responses", 0),
            percentage_correct=evaluation_data.get("percentage_correct", 0.0),
            seniority_distribution=evaluation_data.get("seniority_distribution", {}),
            strengths=evaluation_data.get("strengths", []),
            weaknesses=evaluation_data.get("weaknesses", []),
            recommendation=evaluation_data.get("recommendation", ""),
            summary=evaluation_data.get("summary", "")
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info("Successfully evaluated interview")
        
        return EvaluateInterviewResponse(
            success=True,
            evaluation=interview_evaluation,
            processing_metadata={
                "interview_id": request.interview_id,
                "four_agent_system": request.use_four_agent_system,
                "detailed_analysis": request.include_detailed_analysis,
                "total_questions": len(question_evaluations)
            },
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        logger.error(f"Error in evaluate_interview: {e}")
        
        return EvaluateInterviewResponse(
            success=False,
            evaluation=None,
            error_message=str(e),
            processing_time_ms=processing_time
        )
