"""
Configuration management for external MCP server.

This module handles configuration settings for the standalone MCP server
that communicates with SmartHR via HTTP API.
"""

import os
from typing import Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class MCPServerConfig(BaseSettings):
    """Configuration settings for MCP server."""
    
    # Server settings
    mcp_server_host: str = Field(default="0.0.0.0", env="MCP_SERVER_HOST")
    mcp_server_port: int = Field(default=8001, env="MCP_SERVER_PORT")
    mcp_server_debug: bool = Field(default=False, env="MCP_SERVER_DEBUG")
    
    # SmartHR API settings
    smarthr_api_url: str = Field(default="http://localhost:8080", env="SMARTHR_API_URL")
    smarthr_api_timeout: int = Field(default=30, env="SMARTHR_API_TIMEOUT")
    smarthr_api_key: Optional[str] = Field(None, env="SMARTHR_API_KEY")
    
    # OpenAI/Azure OpenAI settings
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_api_key: Optional[str] = Field(None, env="AZURE_OPENAI_API_KEY")
    azure_openai_deployment_name: Optional[str] = Field(None, env="AZURE_OPENAI_DEPLOYMENT_NAME")
    azure_openai_deployment_name_embeddings: Optional[str] = Field(None, env="AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS")
    azure_openai_api_version: str = Field(default="2023-05-15", env="AZURE_OPENAI_API_VERSION")
    
    # Tool settings
    enable_extract_candidates: bool = Field(default=True, env="ENABLE_EXTRACT_CANDIDATES")
    enable_generate_questions: bool = Field(default=True, env="ENABLE_GENERATE_QUESTIONS")
    enable_evaluate_interview: bool = Field(default=True, env="ENABLE_EVALUATE_INTERVIEW")
    
    # Performance settings
    vector_similarity_threshold: float = Field(default=0.3, env="VECTOR_SIMILARITY_THRESHOLD")
    max_candidates_per_search: int = Field(default=50, env="MAX_CANDIDATES_PER_SEARCH")
    default_questions_per_interview: int = Field(default=8, env="DEFAULT_QUESTIONS_PER_INTERVIEW")
    max_questions_per_interview: int = Field(default=15, env="MAX_QUESTIONS_PER_INTERVIEW")
    
    # Rate limiting
    rate_limit_requests_per_minute: int = Field(default=60, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    rate_limit_burst_size: int = Field(default=10, env="RATE_LIMIT_BURST_SIZE")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    
    # Security
    cors_origins: List[str] = Field(default=["*"])

    # Performance
    request_timeout_seconds: int = Field(default=300, env="REQUEST_TIMEOUT_SECONDS")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


# Global configuration instance
mcp_config = MCPServerConfig()


def get_mcp_config() -> MCPServerConfig:
    """Get MCP server configuration."""
    return mcp_config


def validate_config() -> bool:
    """Validate configuration settings."""
    try:
        # Check SmartHR API URL
        if not mcp_config.smarthr_api_url:
            raise ValueError("SMARTHR_API_URL is required")
        
        # Check OpenAI configuration (optional for some tools)
        has_openai = bool(mcp_config.openai_api_key or 
                         (mcp_config.azure_openai_api_key and mcp_config.azure_openai_endpoint))
        
        if not has_openai:
            print("Warning: No OpenAI configuration found - some features may not work")
        
        return True
        
    except Exception as e:
        print(f"Configuration validation failed: {e}")
        return False


def get_smarthr_api_config() -> dict:
    """Get SmartHR API configuration."""
    return {
        "base_url": mcp_config.smarthr_api_url,
        "timeout": mcp_config.smarthr_api_timeout,
        "api_key": mcp_config.smarthr_api_key
    }


def get_openai_config() -> dict:
    """Get OpenAI configuration for LLM clients."""
    if mcp_config.azure_openai_api_key:
        return {
            "api_type": "azure",
            "api_key": mcp_config.azure_openai_api_key,
            "api_base": mcp_config.azure_openai_endpoint,
            "api_version": mcp_config.azure_openai_api_version,
            "deployment_name": mcp_config.azure_openai_deployment_name,
            "embeddings_deployment": mcp_config.azure_openai_deployment_name_embeddings
        }
    else:
        return {
            "api_type": "openai",
            "api_key": mcp_config.openai_api_key
        }


def get_tool_config() -> dict:
    """Get tool configuration."""
    return {
        "extract_candidates": {
            "enabled": mcp_config.enable_extract_candidates,
            "max_candidates": mcp_config.max_candidates_per_search,
            "similarity_threshold": mcp_config.vector_similarity_threshold
        },
        "generate_questions": {
            "enabled": mcp_config.enable_generate_questions,
            "default_questions": mcp_config.default_questions_per_interview,
            "max_questions": mcp_config.max_questions_per_interview
        },
        "evaluate_interview": {
            "enabled": mcp_config.enable_evaluate_interview
        }
    }
