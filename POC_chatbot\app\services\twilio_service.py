"""
Twilio service for SMS, Voice, and Media Streams integration.

This service provides a unified interface for all Twilio functionality
including SMS messaging, voice calls, and real-time media streaming.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from twilio.rest import Client
from twilio.base.exceptions import TwilioException
from twilio.twiml.messaging_response import MessagingResponse
from twilio.twiml.voice_response import VoiceResponse, Connect, Stream

from app.services.base import (
    BaseService, 
    BaseMessagingService, 
    BaseVoiceService,
    ServiceHealth, 
    ServiceStatus
)
from app.utils.logging import get_logger

logger = get_logger(__name__)


class TwilioService(BaseMessagingService, BaseVoiceService):
    """
    Comprehensive Twilio service for SMS, Voice, and Media Streams.
    
    This service handles all Twilio integrations including:
    - SMS messaging (send/receive)
    - Voice calls (inbound/outbound)
    - Media Streams for real-time audio
    - TwiML generation
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("twilio", config)
        
        # Validate required configuration
        required_keys = ["account_sid", "auth_token", "phone_number"]
        self._validate_config(required_keys)
        
        # Initialize Twilio client
        self.client: Optional[Client] = None
        self.account_sid = config["account_sid"]
        self.auth_token = config["auth_token"]
        self.phone_number = config["phone_number"]
        self.validate_webhooks = config.get("validate_webhooks", False)
    
    async def initialize(self) -> None:
        """Initialize the Twilio service."""
        await super().initialize()
        
        try:
            # Initialize Twilio REST client
            self.client = Client(self.account_sid, self.auth_token)
            
            # Test the connection by fetching account info
            account = self.client.api.accounts(self.account_sid).fetch()
            
            self.logger.info(
                "Twilio service initialized successfully",
                account_sid=self.account_sid,
                phone_number=self.phone_number,
                account_status=account.status
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Twilio service: {e}")
            raise
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the Twilio service."""
        if not self.client:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message="Twilio client not initialized"
            )
        
        try:
            start_time = time.time()
            
            # Test API connectivity by fetching account info
            account = self.client.api.accounts(self.account_sid).fetch()
            
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="Twilio service is operational",
                details={
                    "account_sid": self.account_sid,
                    "account_status": account.status,
                    "phone_number": self.phone_number,
                },
                response_time_ms=response_time
            )
            
        except TwilioException as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"Twilio API error: {e.msg}",
                details={"error_code": e.code}
            )
        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"Twilio health check failed: {str(e)}"
            )
    
    async def send_message(
        self, 
        to: str, 
        message: str, 
        from_number: Optional[str] = None,
        media_url: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Send an SMS message via Twilio.
        
        Args:
            to: Recipient phone number
            message: Message content
            from_number: Sender phone number (uses default if not provided)
            media_url: URL of media to attach (optional)
            **kwargs: Additional Twilio message parameters
            
        Returns:
            Dictionary with message information
        """
        if not self.client:
            raise RuntimeError("Twilio client not initialized")
        
        from_number = from_number or self.phone_number
        
        self.logger.info(
            "Sending SMS message",
            to=to,
            from_number=from_number,
            message_length=len(message),
            has_media=bool(media_url)
        )
        
        try:
            # Prepare message parameters
            message_params = {
                "body": message,
                "from_": from_number,
                "to": to,
            }
            
            # Add media URL if provided
            if media_url:
                message_params["media_url"] = [media_url]
            
            # Add any additional parameters
            message_params.update(kwargs)
            
            # Send message
            twilio_message = self.client.messages.create(**message_params)
            
            result = {
                "sid": twilio_message.sid,
                "status": twilio_message.status,
                "to": twilio_message.to,
                "from": twilio_message.from_,
                "body": twilio_message.body,
                "date_created": twilio_message.date_created.isoformat() if twilio_message.date_created else None,
                "price": twilio_message.price,
                "error_code": twilio_message.error_code,
                "error_message": twilio_message.error_message,
            }
            
            self.logger.info(
                "SMS message sent successfully",
                message_sid=twilio_message.sid,
                status=twilio_message.status,
                to=to
            )
            
            return result
            
        except TwilioException as e:
            self.logger.error(
                "Twilio SMS error",
                error_code=e.code,
                error_message=e.msg,
                to=to,
                from_number=from_number
            )
            raise
        except Exception as e:
            self.logger.error(
                "Error sending SMS message",
                error=str(e),
                to=to,
                from_number=from_number
            )
            raise
    
    async def make_call(
        self, 
        to: str, 
        from_number: str,
        webhook_url: str,
        method: str = "POST",
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Make an outbound voice call via Twilio.
        
        Args:
            to: Recipient phone number
            from_number: Caller phone number
            webhook_url: URL for call webhooks
            method: HTTP method for webhook (default: POST)
            **kwargs: Additional Twilio call parameters
            
        Returns:
            Dictionary with call information
        """
        if not self.client:
            raise RuntimeError("Twilio client not initialized")
        
        self.logger.info(
            "Making outbound call",
            to=to,
            from_number=from_number,
            webhook_url=webhook_url
        )
        
        try:
            # Prepare call parameters
            call_params = {
                "to": to,
                "from_": from_number,
                "url": webhook_url,
                "method": method,
            }
            
            # Add any additional parameters
            call_params.update(kwargs)
            
            # Make call
            call = self.client.calls.create(**call_params)
            
            result = {
                "sid": call.sid,
                "status": call.status,
                "to": call.to,
                "from": call.from_,
                "date_created": call.date_created.isoformat() if call.date_created else None,
                "duration": call.duration,
                "price": call.price,
            }
            
            self.logger.info(
                "Outbound call initiated",
                call_sid=call.sid,
                status=call.status,
                to=to
            )
            
            return result
            
        except TwilioException as e:
            self.logger.error(
                "Twilio call error",
                error_code=e.code,
                error_message=e.msg,
                to=to,
                from_number=from_number
            )
            raise
        except Exception as e:
            self.logger.error(
                "Error making call",
                error=str(e),
                to=to,
                from_number=from_number
            )
            raise
    
    def generate_twiml_response(
        self, 
        message: Optional[str] = None,
        actions: Optional[List[Dict[str, Any]]] = None,
        voice: str = "alice",
        **kwargs: Any
    ) -> str:
        """
        Generate TwiML response for voice calls.
        
        Args:
            message: Message to speak (optional)
            actions: List of TwiML actions (optional)
            voice: Voice to use for speech (default: alice)
            **kwargs: Additional TwiML parameters
            
        Returns:
            TwiML XML string
        """
        response = VoiceResponse()
        
        # Add message if provided
        if message:
            response.say(message, voice=voice)
        
        # Add custom actions if provided
        if actions:
            for action in actions:
                action_type = action.get("type")
                action_params = action.get("params", {})
                
                if action_type == "say":
                    response.say(action_params.get("message", ""), voice=voice)
                elif action_type == "play":
                    response.play(action_params.get("url", ""))
                elif action_type == "pause":
                    response.pause(length=action_params.get("length", 1))
                elif action_type == "hangup":
                    response.hangup()
                elif action_type == "redirect":
                    response.redirect(action_params.get("url", ""))
                elif action_type == "connect_stream":
                    connect = response.connect()
                    connect.stream(url=action_params.get("url", ""))
        
        return str(response)
    
    def generate_sms_twiml_response(self, message: str) -> str:
        """
        Generate TwiML response for SMS.
        
        Args:
            message: Response message
            
        Returns:
            TwiML XML string
        """
        response = MessagingResponse()
        response.message(message)
        return str(response)
    
    async def get_message_status(self, message_sid: str) -> Dict[str, Any]:
        """
        Get the status of a sent message.
        
        Args:
            message_sid: Twilio message SID
            
        Returns:
            Dictionary with message status information
        """
        if not self.client:
            raise RuntimeError("Twilio client not initialized")
        
        try:
            message = self.client.messages(message_sid).fetch()
            
            return {
                "sid": message.sid,
                "status": message.status,
                "to": message.to,
                "from": message.from_,
                "body": message.body,
                "date_created": message.date_created.isoformat() if message.date_created else None,
                "date_sent": message.date_sent.isoformat() if message.date_sent else None,
                "date_updated": message.date_updated.isoformat() if message.date_updated else None,
                "price": message.price,
                "error_code": message.error_code,
                "error_message": message.error_message,
            }
            
        except TwilioException as e:
            self.logger.error(
                "Error fetching message status",
                message_sid=message_sid,
                error_code=e.code,
                error_message=e.msg
            )
            raise
    
    async def get_call_status(self, call_sid: str) -> Dict[str, Any]:
        """
        Get the status of a call.
        
        Args:
            call_sid: Twilio call SID
            
        Returns:
            Dictionary with call status information
        """
        if not self.client:
            raise RuntimeError("Twilio client not initialized")
        
        try:
            call = self.client.calls(call_sid).fetch()
            
            return {
                "sid": call.sid,
                "status": call.status,
                "to": call.to,
                "from": call.from_,
                "date_created": call.date_created.isoformat() if call.date_created else None,
                "date_updated": call.date_updated.isoformat() if call.date_updated else None,
                "duration": call.duration,
                "price": call.price,
                "answered_by": call.answered_by,
            }
            
        except TwilioException as e:
            self.logger.error(
                "Error fetching call status",
                call_sid=call_sid,
                error_code=e.code,
                error_message=e.msg
            )
            raise
