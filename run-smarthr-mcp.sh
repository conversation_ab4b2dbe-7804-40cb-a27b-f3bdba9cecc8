#!/bin/bash

# SmartHR + MCP Server Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}


print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    if command -v docker-compose > /dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker-compose"
    elif docker compose version > /dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker compose"
    else
        print_error "docker-compose or 'docker compose' not found. Please install Docker Compose."
        exit 1
    fi
    print_success "Docker Compose is available: $DOCKER_COMPOSE_CMD"
}

# Function to setup environment file
setup_env() {
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            print_warning ".env file not found. Creating from .env.example..."
            cp .env.example .env
            print_warning "Please edit .env file with your configuration before running again."
            print_warning "At minimum, you need to configure OpenAI/Azure OpenAI settings."
            exit 1
        else
            print_error ".env.example file not found. Cannot create .env file."
            exit 1
        fi
    else
        print_success ".env file found"
    fi
}

# Function to build and start services
start_services() {
    print_status "Building and starting SmartHR + MCP Server..."
    
    $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml up --build -d
    
    if [ $? -eq 0 ]; then
        print_success "Services started successfully!"
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Function to check service health
check_health() {
    print_status "Checking service health..."
    
    # Wait a bit for services to start
    sleep 10
    
    # Check PostgreSQL
    if $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml exec postgres pg_isready -U smarthr -d smarthr > /dev/null 2>&1; then
        print_success "PostgreSQL is healthy"
    else
        print_warning "PostgreSQL is not ready yet"
    fi
    
    # Check Redis
    if $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml exec redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is healthy"
    else
        print_warning "Redis is not ready yet"
    fi
    
    # Check SmartHR Backend
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        print_success "SmartHR Backend is healthy"
    else
        print_warning "SmartHR Backend is not ready yet"
    fi
    
    # Check MCP Server
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        print_success "MCP Server is healthy"
    else
        print_warning "MCP Server is not ready yet"
    fi
}

# Function to show service URLs
show_urls() {
    echo ""
    print_status "Service URLs:"
    echo "  SmartHR Backend:    http://localhost:8080"
    echo "  SmartHR API Docs:   http://localhost:8080/docs"
    echo "  MCP Server:         http://localhost:8001"
    echo "  MCP Server Docs:    http://localhost:8001/docs"
    echo "  PostgreSQL:         localhost:5432 (smarthr/smarthr123)"
    echo "  Redis:              localhost:6379"
    echo ""
    print_status "Health Check URLs:"
    echo "  SmartHR Health:     http://localhost:8080/health"
    echo "  MCP Health:         http://localhost:8001/health"
    echo "  MCP Bridge Health:  http://localhost:8080/mcp/health"
    echo ""
}

# Function to run tests
run_tests() {
    print_status "Running MCP integration tests..."
    
    # Wait for services to be fully ready
    sleep 30
    
    if $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml exec mcp-server python test_client.py --url http://localhost:8001 --test all; then
        print_success "All tests passed!"
    else
        print_warning "Some tests failed. Check the logs above."
    fi
}

# Function to show logs
show_logs() {
    print_status "Showing service logs..."
    $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml logs -f
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml down
    print_success "Services stopped"
}

# Function to clean up (stop and remove volumes)
cleanup() {
    print_status "Cleaning up (removing containers and volumes)..."
    $DOCKER_COMPOSE_CMD -f docker-compose.full-stack.yml down -v --remove-orphans
    print_success "Cleanup completed"
}

# Main script logic
case "${1:-start}" in
    "start")
        print_status "Starting SmartHR + MCP Server Full Stack..."
        check_docker
        check_docker_compose
        setup_env
        start_services
        check_health
        show_urls
        ;;
    "test")
        print_status "Running tests..."
        run_tests
        ;;
    "logs")
        show_logs
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        stop_services
        sleep 5
        start_services
        check_health
        show_urls
        ;;
    "clean")
        cleanup
        ;;
    "health")
        check_health
        ;;
    "urls")
        show_urls
        ;;
    *)
        echo "Usage: $0 {start|test|logs|stop|restart|clean|health|urls}"
        echo ""
        echo "Commands:"
        echo "  start    - Start all services (default)"
        echo "  test     - Run MCP integration tests"
        echo "  logs     - Show service logs"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  clean    - Stop services and remove volumes"
        echo "  health   - Check service health"
        echo "  urls     - Show service URLs"
        exit 1
        ;;
esac
