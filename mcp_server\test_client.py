#!/usr/bin/env python3
"""
Test client for the external MCP server.

This script provides simple tests for all three MCP tools.
"""

import asyncio
import httpx
import json
from datetime import datetime


class MCPTestClient:
    """Test client for MCP server."""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
    
    async def test_health(self):
        """Test health endpoint."""
        print("🔍 Testing health endpoint...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Health check passed")
                    print(f"   Status: {data.get('status')}")
                    print(f"   SmartHR API: {data.get('smarthr_api_status')}")
                    return True
                else:
                    print(f"❌ Health check failed: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ Health check error: {e}")
                return False
    
    async def test_tools_list(self):
        """Test tools list endpoint."""
        print("\n🔍 Testing tools list...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/tools")
                if response.status_code == 200:
                    data = response.json()
                    tools = data.get("tools", [])
                    print(f"✅ Found {len(tools)} tools:")
                    for tool in tools:
                        print(f"   - {tool['name']}: {tool['enabled']}")
                    return True
                else:
                    print(f"❌ Tools list failed: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ Tools list error: {e}")
                return False
    
    async def test_extract_candidates(self):
        """Test extract candidates tool."""
        print("\n🔍 Testing extract candidates...")
        
        request_data = {
            "position": {
                "position_id": "test_pos_123",
                "title": "Senior Python Developer",
                "description": "Looking for experienced Python developer with FastAPI and PostgreSQL experience",
                "required_skills": ["Python", "FastAPI", "PostgreSQL"],
                "preferred_skills": ["Docker", "AWS", "React"],
                "seniority_level": "senior",
                "location": "Remote",
                "department": "Engineering"
            },
            "max_candidates": 5,
            "min_similarity_score": 0.3,
            "include_skills_analysis": True,
            "filter_by_seniority": False
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/extract-candidates",
                    json=request_data
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        candidates = data.get("candidates", [])
                        print(f"✅ Extract candidates succeeded")
                        print(f"   Found {len(candidates)} candidates")
                        print(f"   Processing time: {data.get('processing_time_ms', 0):.2f}ms")
                        
                        if candidates:
                            print("   Top candidate:")
                            top = candidates[0]
                            print(f"     - Name: {top.get('name')}")
                            print(f"     - Similarity: {top.get('similarity_score', 0):.3f}")
                            print(f"     - Seniority: {top.get('seniority_match')}")
                        
                        return True
                    else:
                        print(f"❌ Extract candidates failed: {data.get('error_message')}")
                        return False
                else:
                    print(f"❌ Extract candidates HTTP error: {response.status_code}")
                    print(f"   Response: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ Extract candidates error: {e}")
                return False
    
    async def test_generate_questions(self):
        """Test generate questions tool."""
        print("\n🔍 Testing generate questions...")
        
        request_data = {
            "position_id": "test_pos_123",
            "num_questions": 5,
            "categories": [],
            "target_seniority": "senior",
            "include_soft_skills": True,
            "custom_requirements": "Focus on Python and system design"
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/generate-questions",
                    json=request_data
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        questions = data.get("questions", [])
                        print(f"✅ Generate questions succeeded")
                        print(f"   Generated {len(questions)} questions")
                        print(f"   Processing time: {data.get('processing_time_ms', 0):.2f}ms")
                        
                        if questions:
                            print("   Sample question:")
                            q = questions[0]
                            print(f"     - Q{q.get('question_number')}: {q.get('question_text')[:100]}...")
                            print(f"     - Category: {q.get('category')}")
                            print(f"     - Difficulty: {q.get('difficulty_level')}")
                        
                        return True
                    else:
                        print(f"❌ Generate questions failed: {data.get('error_message')}")
                        return False
                else:
                    print(f"❌ Generate questions HTTP error: {response.status_code}")
                    print(f"   Response: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ Generate questions error: {e}")
                return False
    
    async def test_evaluate_interview(self):
        """Test evaluate interview tool."""
        print("\n🔍 Testing evaluate interview...")
        
        request_data = {
            "interview_id": "test_int_456",
            "transcript": """
            Q: Can you explain the difference between a list and a tuple in Python?
            A: A list is mutable, meaning you can change its contents after creation, while a tuple is immutable. Lists use square brackets and tuples use parentheses.
            
            Q: How would you optimize a slow database query?
            A: I would start by analyzing the query execution plan, check for missing indexes, consider query rewriting, and look at table statistics. I might also consider partitioning for large tables.
            """,
            "questions": [
                "Can you explain the difference between a list and a tuple in Python?",
                "How would you optimize a slow database query?"
            ],
            "position_id": "test_pos_123",
            "candidate_id": "test_cand_789",
            "use_four_agent_system": True,
            "include_detailed_analysis": True
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/evaluate-interview",
                    json=request_data
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        evaluation = data.get("evaluation", {})
                        print(f"✅ Evaluate interview succeeded")
                        print(f"   Overall seniority: {evaluation.get('overall_seniority')}")
                        print(f"   Confidence: {evaluation.get('confidence_score', 0):.3f}")
                        print(f"   Valid responses: {evaluation.get('valid_responses')}/{evaluation.get('total_questions')}")
                        print(f"   Processing time: {data.get('processing_time_ms', 0):.2f}ms")
                        
                        if evaluation.get("recommendation"):
                            print(f"   Recommendation: {evaluation['recommendation'][:100]}...")
                        
                        return True
                    else:
                        print(f"❌ Evaluate interview failed: {data.get('error_message')}")
                        return False
                else:
                    print(f"❌ Evaluate interview HTTP error: {response.status_code}")
                    print(f"   Response: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ Evaluate interview error: {e}")
                return False
    
    async def run_all_tests(self):
        """Run all tests."""
        print(f"🚀 Starting MCP Server Tests")
        print(f"   Server: {self.base_url}")
        print(f"   Time: {datetime.now().isoformat()}")
        print("=" * 50)
        
        results = []
        
        # Test health
        results.append(await self.test_health())
        
        # Test tools list
        results.append(await self.test_tools_list())
        
        # Test individual tools
        results.append(await self.test_extract_candidates())
        results.append(await self.test_generate_questions())
        results.append(await self.test_evaluate_interview())
        
        # Summary
        passed = sum(results)
        total = len(results)
        
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {passed}/{total} passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return True
        else:
            print("⚠️  Some tests failed. Check the logs above.")
            return False


async def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test MCP Server")
    parser.add_argument("--url", default="http://localhost:8001", help="MCP server URL")
    parser.add_argument("--test", choices=["health", "tools", "extract", "generate", "evaluate", "all"], 
                       default="all", help="Test to run")
    
    args = parser.parse_args()
    
    client = MCPTestClient(args.url)
    
    if args.test == "health":
        await client.test_health()
    elif args.test == "tools":
        await client.test_tools_list()
    elif args.test == "extract":
        await client.test_extract_candidates()
    elif args.test == "generate":
        await client.test_generate_questions()
    elif args.test == "evaluate":
        await client.test_evaluate_interview()
    else:
        await client.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
