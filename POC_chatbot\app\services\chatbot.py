"""
Chatbot service for handling conversation logic and AI responses.

This service manages conversation state, processes user messages,
and generates appropriate responses using Groq LLM.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from app.services.base import BaseService, ServiceHealth, ServiceStatus
from app.services.groq_service import groq_service
from app.config import settings


@dataclass
class Message:
    """Represents a single message in a conversation."""
    content: str
    role: str  # 'user' or 'assistant'
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversationContext:
    """Represents the context of a conversation."""
    session_id: str
    messages: List[Message] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_message(self, message: Message) -> None:
        """Add a message to the conversation."""
        self.messages.append(message)
        self.last_activity = datetime.now()
        
        # Keep only the last N messages to prevent memory issues
        if len(self.messages) > settings.max_conversation_history * 2:  # *2 for user+assistant pairs
            self.messages = self.messages[-settings.max_conversation_history * 2:]
    
    def get_recent_messages(self, count: Optional[int] = None) -> List[Message]:
        """Get recent messages from the conversation."""
        if count is None:
            count = settings.max_conversation_history
        return self.messages[-count:] if self.messages else []
    
    def is_expired(self) -> bool:
        """Check if the conversation has expired."""
        timeout = timedelta(minutes=settings.session_timeout_minutes)
        return datetime.now() - self.last_activity > timeout


class ChatbotService(BaseService):
    """
    Service for handling chatbot conversations and AI responses.
    
    This service manages conversation state, processes user messages,
    and generates responses using configurable AI or rule-based logic.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("chatbot", config)
        self.conversations: Dict[str, ConversationContext] = {}
        self.system_message = settings.chatbot_system_message
        
        # Placeholder responses for demonstration
        self.placeholder_responses = [
            "Hello! I'm your AI assistant. How can I help you today?",
            "That's interesting! Tell me more about that.",
            "I understand. Is there anything specific you'd like to know?",
            "Thanks for sharing that with me. What else can I help you with?",
            "I'm here to help! Feel free to ask me anything.",
            "That sounds great! How can I assist you further?",
            "I appreciate you reaching out. What would you like to discuss?",
            "Interesting question! Let me think about that for you.",
            "I'm glad you asked! Is there more context you can provide?",
            "Thank you for your message. How else can I be of service?",
        ]
    
    async def initialize(self) -> None:
        """Initialize the chatbot service."""
        await super().initialize()
        self.logger.info("Chatbot service initialized with placeholder responses")
        
        # Start cleanup task for expired conversations
        asyncio.create_task(self._cleanup_expired_conversations())
    
    async def health_check(self) -> ServiceHealth:
        """Check the health of the chatbot service."""
        try:
            # Simple health check - verify we can create a test conversation
            test_session = f"health_check_{int(time.time())}"
            context = self._get_or_create_conversation(test_session)
            
            # Clean up test conversation
            if test_session in self.conversations:
                del self.conversations[test_session]
            
            return ServiceHealth(
                status=ServiceStatus.HEALTHY,
                message="Chatbot service is operational",
                details={
                    "active_conversations": len(self.conversations),
                    "system_message_configured": bool(self.system_message),
                }
            )
        except Exception as e:
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                message=f"Chatbot service health check failed: {str(e)}"
            )
    
    async def process_message(
        self, 
        session_id: str, 
        message: str, 
        channel: str = "unknown",
        user_metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Process a user message and generate a response.
        
        Args:
            session_id: Unique session identifier
            message: User's message content
            channel: Communication channel (sms, voice, etc.)
            user_metadata: Additional user information
            
        Returns:
            Generated response message
        """
        self.logger.info(
            "Processing message",
            session_id=session_id,
            message_length=len(message),
            channel=channel
        )
        
        # Get or create conversation context
        context = self._get_or_create_conversation(session_id)
        
        # Add user message to conversation
        user_message = Message(
            content=message,
            role="user",
            metadata={
                "channel": channel,
                "user_metadata": user_metadata or {}
            }
        )
        context.add_message(user_message)
        
        # Generate response
        response = await self._generate_response(context, message, channel)
        
        # Add assistant response to conversation
        assistant_message = Message(
            content=response,
            role="assistant",
            metadata={"channel": channel}
        )
        context.add_message(assistant_message)
        
        self.logger.info(
            "Generated response",
            session_id=session_id,
            response_length=len(response)
        )
        
        return response
    
    def get_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get conversation history for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of message dictionaries
        """
        if session_id not in self.conversations:
            return []
        
        context = self.conversations[session_id]
        return [
            {
                "content": msg.content,
                "role": msg.role,
                "timestamp": msg.timestamp.isoformat(),
                "metadata": msg.metadata
            }
            for msg in context.messages
        ]
    
    def clear_conversation(self, session_id: str) -> bool:
        """
        Clear conversation history for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if conversation was cleared, False if not found
        """
        if session_id in self.conversations:
            del self.conversations[session_id]
            self.logger.info("Conversation cleared", session_id=session_id)
            return True
        return False
    
    def _get_or_create_conversation(self, session_id: str) -> ConversationContext:
        """Get existing conversation or create a new one."""
        if session_id not in self.conversations:
            self.conversations[session_id] = ConversationContext(session_id=session_id)
            self.logger.info("New conversation created", session_id=session_id)
        
        return self.conversations[session_id]
    
    async def _generate_response(
        self,
        context: ConversationContext,
        message: str,
        channel: str
    ) -> str:
        """
        Generate a response to the user's message using Groq LLM.
        """
        try:
            # Ensure Groq service is initialized
            if not groq_service.is_initialized():
                await groq_service.initialize()

            # Prepare conversation messages for Groq
            groq_messages = []

            # Add recent conversation history (last 10 messages to stay within context limits)
            recent_messages = context.messages[-10:] if len(context.messages) > 10 else context.messages

            for msg in recent_messages:
                groq_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })

            # Add current user message
            groq_messages.append({
                "role": "user",
                "content": message
            })

            # Generate response using Groq
            response = await groq_service.generate_response(
                messages=groq_messages,
                system_message=settings.chatbot_system_message,
                temperature=0.7,
                max_tokens=512  # Shorter responses for SMS/voice
            )

            # Ensure response is not too long for SMS (160 char limit consideration)
            if channel == "sms" and len(response) > 300:
                response = response[:297] + "..."

            return response

        except Exception as e:
            self.logger.error(f"Error generating Groq response: {e}")

            # Fallback to simple rule-based response
            return self._generate_fallback_response(message, context)

    def _generate_fallback_response(self, message: str, context: ConversationContext) -> str:
        """
        Generate a fallback response when Groq is unavailable.
        """
        message_lower = message.lower().strip()

        # Greeting responses
        if any(greeting in message_lower for greeting in ["hello", "hi", "hey", "good morning", "good afternoon"]):
            return "Hello! I'm your AI assistant. How can I help you today?"

        # Question responses
        if message_lower.endswith("?"):
            return "That's a great question! I'm here to help however I can."

        # Goodbye responses
        if any(goodbye in message_lower for goodbye in ["bye", "goodbye", "see you", "thanks", "thank you"]):
            return "You're welcome! Feel free to reach out anytime. Have a great day!"

        # Help requests
        if any(help_word in message_lower for help_word in ["help", "assist", "support"]):
            return "I'm here to help! You can ask me questions or just have a conversation. What would you like to know?"

        # Default responses
        fallback_responses = [
            "I understand. Can you tell me more about that?",
            "That's interesting! What else would you like to discuss?",
            "I'm here to help. Is there anything specific you'd like to know?",
            "Thanks for sharing that with me. How can I assist you further?",
            "I appreciate you reaching out. What can I help you with today?"
        ]

        message_count = len([msg for msg in context.messages if msg.role == "user"])
        response_index = (message_count - 1) % len(fallback_responses)

        return fallback_responses[response_index]

    async def _cleanup_expired_conversations(self) -> None:
        """Periodically clean up expired conversations."""
        while True:
            try:
                expired_sessions = [
                    session_id for session_id, context in self.conversations.items()
                    if context.is_expired()
                ]
                
                for session_id in expired_sessions:
                    del self.conversations[session_id]
                    self.logger.info("Expired conversation cleaned up", session_id=session_id)
                
                if expired_sessions:
                    self.logger.info(f"Cleaned up {len(expired_sessions)} expired conversations")
                
                # Sleep for 5 minutes before next cleanup
                await asyncio.sleep(300)
                
            except Exception as e:
                self.logger.error(f"Error during conversation cleanup: {e}")
                await asyncio.sleep(60)  # Shorter sleep on error
